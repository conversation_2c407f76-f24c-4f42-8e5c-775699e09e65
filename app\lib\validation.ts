/**
 * Excel验证服务 - 基于官方Univer API
 */

import { log } from '@/app/lib/logger'

// 定义 Univer 相关类型
export interface UniverWorkbook {
  getActiveSheet(): UniverWorksheet | null;
  getSheetBySheetId?(sheetId: string): UniverWorksheet | null;
  getSheetCount?(): number;
}

export interface UniverFilter {
  getRange(): { getA1Notation(): string };
  getFilteredOutRows(): unknown[];
  getColumnFilterCriteria(colIndex: number): unknown;
}

// 定义筛选器的类型
interface FilterAPI {
  getRange?(): { getA1Notation?(): string };
  getFilteredOutRows?(): unknown[];
  getColumnFilterCriteria?(colIndex: number): unknown;
}

export interface UniverWorksheet {
  getSheetId(): string;
  getName(): string;
  getCellData(): Record<string, Record<string, UniverCellData>>;
  getRange(range: string): UniverRange;
  getCell(row: number, col: number): UniverCellData;
  getFilter(): unknown | null;
}

export interface UniverCellData {
  v?: unknown; // value
  f?: string; // formula
  s?: UniverCellStyle; // style
  t?: number; // type
  p?: { // rich text data
    body?: {
      dataStream?: string;
    };
  };
}

export interface UniverCellStyle {
  bg?: { rgb?: string };
  cl?: { rgb?: string };
  ff?: string; // font family
  fs?: number; // font size
  bl?: number | boolean; // bold
  it?: number | boolean; // italic
  n?: { pattern?: string }; // number format
  bold?: number | boolean; // alternative bold
  italic?: number | boolean; // alternative italic
  fontWeight?: string | number; // font weight
  fontStyle?: string; // font style
  ft?: { bl?: number | boolean; it?: number | boolean }; // font style
  ht?: number; // horizontal alignment
  vt?: number; // vertical alignment
  bd?: { // border data
    t?: { s?: number; cl?: { rgb?: string } }; // top border
    b?: { s?: number; cl?: { rgb?: string } }; // bottom border
    l?: { s?: number; cl?: { rgb?: string } }; // left border
    r?: { s?: number; cl?: { rgb?: string } }; // right border
  };
  [key: string]: unknown; // allow additional properties
}

export interface UniverRange {
  getValue(): unknown;
  getValues(): unknown[][];
  getFormula(): string;
  getFormulas(): string[][];
  getBackgroundColor(): string;
  getBackgroundColors(): string[][];
  getCellData(): UniverCellData;
  getNumberFormat(): string;
  getDisplayValue(): string;
  getConditionalFormattingRules(): unknown[];
  isMerged(): boolean;
  getWrap(): boolean;
}

export interface ConditionalFormatRule {
  type: string;
  value?: unknown;
  minValue?: unknown;
  maxValue?: unknown;
  color: string;
}

export interface ValidationRule {
  type: string
  cell?: string
  expectedValue?: unknown
  expectedFormula?: string
  expectedFormat?: string
  expectedStyle?: Record<string, unknown>
  dataRange?: string
  expectedType?: string
  expectedFields?: string[]
  // 透视表严格验证相关字段
  strictValidation?: boolean
  expectedRowHeaders?: string[]
  expectedColumnHeaders?: string[]
  expectedTotalValue?: number
  // 筛选验证相关字段
  expectedVisibleRows?: number
  expectedFilteredData?: Array<Record<string, unknown>>
  // 排序验证相关字段
  expectedOrder?: string[] | number[]
  sortColumn?: string
  sortDirection?: 'asc' | 'desc'
  // 条件格式验证相关字段
  expectedBackgroundColor?: string
  expectedTextColor?: string
  conditionRange?: string
  // 多单元格对齐验证相关字段
  cells?: Array<{
    cell?: string;
    range?: string;
    expectedAlignment?: string;
    expectedBorder?: string;
    expectedBorderColor?: string;
    description?: string;
    // 文本换行验证相关字段
    wrapType?: 'auto' | 'manual';
    expectedText?: string;
  }>
  // 兼容旧的字段名
  column?: string
  direction?: 'asc' | 'desc'
  sorts?: Array<{ column: string; direction: 'asc' | 'desc' }>
  range?: string
  condition?: string
  value?: unknown
  expectedFormattedCells?: string[]
  conditions?: ConditionalFormatRule[]
  expectedResults?: Record<string, string[]>
  formulaCell?: string
  expectedResult?: unknown
  // 数据验证相关属性
  validationType?: string
  source?: string
  allowDropdown?: boolean
  // 单元格合并验证相关字段
  mergedRanges?: Array<{
    range: string
    description: string
  }>
  // 文本换行验证相关字段
  wrapType?: 'auto' | 'manual'
  expectedText?: string
  // 公式填充验证相关字段
  expectedFormulas?: Array<{
    cell: string
    formula: string
  }>
}

export interface ValidationResult {
  success: boolean
  message: string
  details?: Record<string, unknown>
}

export interface UniverAPI {
  getActiveWorkbook(): UniverWorkbook | null;
}

export class ExcelValidationService {
  constructor(private univerAPI: UniverAPI) {}

  /**
   * 提取 Univer 单元格的实际值
   */
  private extractCellValue(cellValue: unknown): unknown {
    if (cellValue && typeof cellValue === 'object' && 'v' in cellValue) {
      return (cellValue as { v: unknown }).v;
    }
    return cellValue;
  }

  /**
   * 验证任务
   */
  async validateTask(rule: ValidationRule): Promise<ValidationResult> {
    log.validation('开始验证任务:', rule)

    try {
      switch (rule.type) {
        case 'cellValue':
        case 'input': // input类型等同于cellValue验证
          return await this.validateCellValue(rule)
        case 'cellFormula':
          return await this.validateCellFormula(rule)
        case 'cellFormat':
          return await this.validateCellFormat(rule)
        case 'cellStyle':
          return await this.validateCellStyle(rule)
        case 'chart':
          return await this.validateChart(rule)
        case 'pivotTable':
          return await this.validatePivotTable(rule)
        case 'filter':
          return await this.validateFilter(rule)
        case 'sort':
          return await this.validateSort(rule)
        case 'multiSort':
          return await this.validateMultiSort(rule)
        case 'conditional_format':
        case 'conditionalFormat':
        case 'multiConditionalFormat':
          return await this.validateConditionalFormat(rule)
        case 'multiCellAlignment':
          return await this.validateMultiCellAlignment(rule)
        case 'multiBorder':
          return await this.validateMultiBorder(rule)
        case 'dataValidation':
          return await this.validateDataValidation(rule)
        case 'cellMerge':
          return await this.validateCellMerge(rule)
        case 'textWrap':
          return await this.validateTextWrap(rule)
        case 'formulaFill':
          return await this.validateFormulaFill(rule)
        default:
          return {
            success: false,
            message: `未知的验证类型: ${rule.type}`
          }
      }
    } catch (error) {
      log.error('验证过程中发生错误:', error)
      return {
        success: false,
        message: '验证过程中发生错误，请重试',
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证单元格值
   */
  private async validateCellValue(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell || rule.expectedValue === undefined) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置或期望值'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook();
      if (!workbook) throw new Error('未获取到工作簿');
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取活动工作表'
        }
      }

      const range = worksheet.getRange(rule.cell)
      if (!range) {
        return {
          success: false,
          message: `无法获取单元格 ${rule.cell}`
        }
      }

      const cellValue = range.getValue()
      const actualValue = this.extractCellValue(cellValue)

      // 类型转换和比较
      const expectedValue = rule.expectedValue
      const isMatch = this.compareValues(actualValue, expectedValue)

      return {
        success: isMatch,
        message: isMatch
          ? '单元格值验证通过！'
          : `单元格 ${rule.cell} 的值不正确。期望: "${expectedValue}"，实际: "${actualValue}"`,
        details: {
          cell: rule.cell,
          expected: expectedValue,
          actual: actualValue
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `验证单元格值时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证单元格公式
   */
  private async validateCellFormula(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell || !rule.expectedFormula) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置或期望公式'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook();
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }
      const range = worksheet.getRange(rule.cell)

      // 获取单元格公式
      const cellData = range.getCellData()
      const actualFormula = cellData?.f || ''

      // 标准化公式格式（去除空格，统一大小写）
      const normalizeFormula = (formula: string) => {
        return formula.replace(/\s+/g, '').toUpperCase()
      }

      const expectedNormalized = normalizeFormula(rule.expectedFormula)
      const actualNormalized = normalizeFormula(actualFormula)

      const isFormulaMatch = actualNormalized === expectedNormalized

      // 如果有期望值，也验证计算结果
      let isValueMatch = true
      let actualValue = null
      if (rule.expectedValue !== undefined) {
        const cellValue = range.getValue()
        actualValue = this.extractCellValue(cellValue)
        isValueMatch = this.compareValues(actualValue, rule.expectedValue)
      }

      const success = isFormulaMatch && isValueMatch

      let message = ''
      if (!isFormulaMatch) {
        message = `公式不正确。期望: "${rule.expectedFormula}"，实际: "${actualFormula}"`
      } else if (!isValueMatch) {
        message = `公式正确但计算结果不对。期望结果: ${rule.expectedValue}，实际结果: ${actualValue}`
      } else {
        message = '公式验证通过！'
      }

      return {
        success,
        message,
        details: {
          cell: rule.cell,
          expectedFormula: rule.expectedFormula,
          actualFormula,
          expectedValue: rule.expectedValue,
          actualValue
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `验证公式时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证单元格格式
   */
  private async validateCellFormat(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell || !rule.expectedFormat) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置或期望格式'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook();
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }
      const range = worksheet.getRange(rule.cell)

      // 获取单元格格式信息
      const cellData = range.getCellData()
      const numberFormat = range.getNumberFormat() || cellData?.s?.n?.pattern || ''

      // 获取单元格的显示值
      const cellValue = range.getValue()
      const actualValue = this.extractCellValue(cellValue)
      const displayValue = actualValue !== undefined && actualValue !== null ? actualValue.toString() : ''

      // 根据期望格式类型进行验证
      let isFormatMatch = false
      let formatDescription = ''

      switch (rule.expectedFormat) {
        case 'currency':
          // 检查格式字符串或显示值是否包含货币符号
          isFormatMatch = this.isCurrencyFormat(numberFormat) || this.isCurrencyFormat(displayValue)
          formatDescription = '货币格式'
          break
        case 'percentage':
          isFormatMatch = this.isPercentageFormat(numberFormat) || this.isPercentageFormat(displayValue)
          formatDescription = '百分比格式'
          break
        case 'date':
          isFormatMatch = this.isDateFormat(numberFormat) || this.isDateFormat(displayValue)
          formatDescription = '日期格式'
          break
        default:
          isFormatMatch = numberFormat === rule.expectedFormat
          formatDescription = rule.expectedFormat
      }

      return {
        success: isFormatMatch,
        message: isFormatMatch
          ? `${formatDescription}验证通过！`
          : `单元格 ${rule.cell} 的格式不正确。期望: ${formatDescription}，实际格式: "${numberFormat}"，显示值: "${displayValue}"`,
        details: {
          cell: rule.cell,
          expectedFormat: rule.expectedFormat,
          actualFormat: numberFormat,
          displayValue: displayValue
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `验证格式时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证单元格样式
   */
  private async validateCellStyle(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell || !rule.expectedStyle) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置或期望样式'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook();
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }
      const range = worksheet.getRange(rule.cell)

      // 获取单元格数据和样式信息
      const cellData = range.getCellData()
      let style: UniverCellStyle = {}

      // 根据Univer文档，样式可能是ID引用或直接的样式对象
      if (cellData?.s) {
        if (typeof cellData.s === 'string') {
          // 如果是字符串，说明是样式ID，需要从工作簿样式表中获取
          const workbook = this.univerAPI.getActiveWorkbook()
          let styles = {}

          try {
            // 使用save()方法获取工作簿数据，按照文档建议
            try {
              const workbookData = await (workbook as unknown as { save(): Promise<{ styles?: Record<string, UniverCellStyle> }> }).save()
              styles = workbookData?.styles || {}
              log.debug('使用save()获取样式表:', { totalStyles: Object.keys(styles).length })
            } catch (saveError) {
              log.debug('save()方法失败，尝试其他方法:', saveError)

              // 备用方法：尝试从Univer实例获取
              const univerInstance = (this.univerAPI as unknown as { _univerInstance?: unknown })?._univerInstance
              if (univerInstance) {
                const currentWorkbook = (univerInstance as { getCurrentUniverSheetInstance(): unknown }).getCurrentUniverSheetInstance()
                const workbookSnapshot = (currentWorkbook as { save?(): unknown; getSnapshot?(): unknown })?.save?.() || (currentWorkbook as { save?(): unknown; getSnapshot?(): unknown })?.getSnapshot?.()
                styles = (workbookSnapshot as { styles?: Record<string, UniverCellStyle> })?.styles || {}
              }
            }

            log.debug('获取到的样式表:', { totalStyles: Object.keys(styles).length, styleId: cellData.s })
          } catch (error) {
            log.debug('获取样式表失败:', error)
          }

          style = styles[cellData.s] || {}
          log.debug('从样式表获取样式:', { styleId: cellData.s, style, hasStyle: !!styles[cellData.s] })
        } else {
          // 如果是对象，直接使用
          style = cellData.s
          log.debug('直接使用样式对象:', style)
        }
      }

      log.debug('单元格数据和样式:', { cellData, style, styleType: typeof cellData?.s })

      const validationResults = []

      // 验证粗体
      if (rule.expectedStyle.bold !== undefined) {
        // 只检查标准的粗体属性，避免过于宽松的验证
        const isBold = style.bl === 1 || style.bl === true ||
                      style.bold === 1 || style.bold === true ||
                      style.fontWeight === 'bold' || style.fontWeight === 700 ||
                      (style.ft && (style.ft.bl === 1 || style.ft.bl === true))
        const expectedBold = rule.expectedStyle.bold

        log.validation('粗体验证调试信息:', {
          cell: rule.cell,
          style: style,
          isBold: isBold,
          expectedBold: expectedBold,
          styleKeys: Object.keys(style),
          styleType: typeof style
        })

        validationResults.push({
          property: 'bold',
          expected: expectedBold,
          actual: isBold,
          match: isBold === expectedBold
        })
      }

      // 验证斜体
      if (rule.expectedStyle.italic !== undefined) {
        // 支持多种斜体属性格式
        const isItalic = style.it === 1 || style.it === true ||
                         style.italic === 1 || style.italic === true ||
                         style.fontStyle === 'italic' ||
                         (style.ft && (style.ft.it === 1 || style.ft.it === true))
        const expectedItalic = rule.expectedStyle.italic

        log.validation('斜体验证调试信息:', {
          cell: rule.cell,
          style: style,
          isItalic: isItalic,
          expectedItalic: expectedItalic,
          styleKeys: Object.keys(style)
        })

        validationResults.push({
          property: 'italic',
          expected: expectedItalic,
          actual: isItalic,
          match: isItalic === expectedItalic
        })
      }

      // 验证字体系列
      if (rule.expectedStyle.fontFamily) {
        // 从样式对象获取字体系列 (ff属性)
        const actualFontFamily = style.ff || ''
        const expectedFontFamily = rule.expectedStyle.fontFamily

        log.validation('字体系列获取:', { actualFontFamily, expectedFontFamily, style })

        // 字体名称标准化
        const normalizeFont = (font: string) => {
          if (!font) return ''
          return font.toLowerCase()
            .replace(/['"]/g, '')
            .replace(/\s+/g, '')
            .trim()
        }

        const actualNormalized = normalizeFont(actualFontFamily)
        const expectedNormalized = normalizeFont(String(expectedFontFamily))

        // 字体匹配逻辑 - 严格匹配，只有实际设置了字体时才认为匹配
        let isMatch = false

        if (actualFontFamily && actualNormalized) {
          // 精确匹配
          if (actualNormalized === expectedNormalized) {
            isMatch = true
          }
          // 特殊处理宋体的各种表示方式 - 严格匹配，排除新宋体等其他字体
          else if (expectedNormalized === '宋体') {
            // 只有这些特定的字体名称才认为是宋体，明确排除nsimsun(新宋体)
            if (actualNormalized === 'nsimsun' || actualNormalized.includes('nsimsun')) {
              isMatch = false
            } else {
              isMatch = actualNormalized === 'simsun' ||
                       actualNormalized === '宋体' ||
                       actualNormalized === 'songti' ||
                       actualNormalized === 'st宋体' ||
                       actualNormalized === 'stsong'
            }
          }
          else if (actualNormalized === '宋体') {
            // 反向匹配
            isMatch = expectedNormalized === 'simsun' ||
                     expectedNormalized === '宋体' ||
                     expectedNormalized === 'songti' ||
                     expectedNormalized === 'st宋体' ||
                     expectedNormalized === 'stsong'
          }
          // 其他字体的精确匹配
          else {
            isMatch = actualNormalized === expectedNormalized
          }
        }

        log.validation('字体验证调试信息:', {
          cell: rule.cell,
          actualFontFamily,
          expectedFontFamily,
          actualNormalized,
          expectedNormalized,
          isMatch,
          hasActualFont: !!actualFontFamily,
          styleObject: style
        })

        validationResults.push({
          property: 'fontFamily',
          expected: expectedFontFamily,
          actual: actualFontFamily || '(未设置字体)',
          match: isMatch
        })
      }

      // 验证字体颜色
      if (rule.expectedStyle.color) {
        // 根据Univer文档，从样式对象获取字体颜色 (cl.rgb属性)
        const actualColor = style.cl?.rgb || ''

        const expectedColor = rule.expectedStyle.color

        // 颜色值标准化 - 统一转换为小写并去除所有#前缀进行比较
        const normalizeColor = (color: string) => {
          if (!color) return ''
          return color.toLowerCase().replace(/#/g, '')  // 使用全局替换去除所有#号
        }

        const actualNormalized = normalizeColor(actualColor)
        const expectedNormalized = normalizeColor(String(expectedColor))

        const isMatch = actualNormalized === expectedNormalized

        log.validation('字体颜色验证调试信息:', {
          cell: rule.cell,
          actualColor,
          expectedColor,
          actualNormalized,
          expectedNormalized,
          isMatch,
          styleObject: style
        })

        validationResults.push({
          property: 'color',
          expected: expectedColor,
          actual: actualColor || '(未设置颜色)',
          match: isMatch
        })
      }

      // 验证背景色
      if (rule.expectedStyle.backgroundColor) {
        // 根据Univer文档，从样式对象获取背景颜色 (bg.rgb属性)
        const actualBgColor = style.bg?.rgb || ''

        const expectedBgColor = rule.expectedStyle.backgroundColor

        // 颜色值标准化 - 统一转换为小写并去除所有#前缀进行比较
        const normalizeColor = (color: string) => {
          if (!color) return ''
          return color.toLowerCase().replace(/#/g, '')  // 使用全局替换去除所有#号
        }

        const actualNormalized = normalizeColor(actualBgColor)
        const expectedNormalized = normalizeColor(String(expectedBgColor))

        const isMatch = actualNormalized === expectedNormalized

        log.validation('背景颜色验证调试信息:', {
          cell: rule.cell,
          actualBgColor,
          expectedBgColor,
          actualNormalized,
          expectedNormalized,
          isMatch,
          styleObject: style
        })

        validationResults.push({
          property: 'backgroundColor',
          expected: expectedBgColor,
          actual: actualBgColor || '(未设置背景色)',
          match: isMatch
        })
      }

      const allMatch = validationResults.every(result => result.match)
      const failedValidations = validationResults.filter(result => !result.match)

      let message = ''
      if (allMatch) {
        message = '样式验证通过！'
      } else {
        const failedDetails = failedValidations.map(v =>
          `${v.property}(期望:${v.expected}, 实际:${v.actual})`
        ).join('、')
        message = `单元格 ${rule.cell} 的样式不正确。失败的属性: ${failedDetails}。当前样式对象: ${JSON.stringify(style)}`
      }

      return {
        success: allMatch,
        message,
        details: {
          cell: rule.cell,
          validationResults,
          actualStyle: style
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `验证样式时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证图表
   */
  private async validateChart(rule: ValidationRule): Promise<ValidationResult> {
    try {
      // 简化的图表验证逻辑
      // 检查是否选择了正确的数据范围
      if (rule.dataRange) {
        const workbook = this.univerAPI.getActiveWorkbook()
        if (!workbook) {
          return {
            success: false,
            message: '无法获取工作簿，请确保Excel组件已正确加载'
          }
        }
        const worksheet = workbook.getActiveSheet()
        if (!worksheet) {
          return {
            success: false,
            message: '无法获取工作表，请确保Excel组件已正确加载'
          }
        }

        // 验证数据范围是否有数据
        const range = worksheet.getRange(rule.dataRange)
        const values = range.getValues()

        if (!values || values.length === 0) {
          return {
            success: false,
            message: `数据范围 ${rule.dataRange} 中没有数据。请确保数据范围正确。`,
            details: {
              dataRange: rule.dataRange,
              expectedType: rule.expectedType
            }
          }
        }
      }

      // 检查DOM中是否存在图表相关元素
      const chartSelectors = [
        '.univer-chart',
        '.echarts-chart',
        '.chart-container',
        '[data-chart-id]',
        'canvas[data-zr-dom-id]', // ECharts canvas
        '.univer-drawing-object' // Univer绘图对象
      ]

      let chartFound = false
      for (const selector of chartSelectors) {
        const elements = document.querySelectorAll(selector)
        if (elements.length > 0) {
          chartFound = true
          log.debug(`找到图表元素: ${selector}, 数量: ${elements.length}`)
          break
        }
      }

      if (!chartFound) {
        return {
          success: false,
          message: '未找到图表。请按照操作步骤创建图表：\n1. 选择数据范围\n2. 点击"插入"选项卡\n3. 选择"图表"\n4. 选择合适的图表类型',
          details: {
            expectedType: rule.expectedType,
            dataRange: rule.dataRange,
            hint: '请确保已经插入了图表'
          }
        }
      }

      return {
        success: true,
        message: '图表创建成功！任务完成。',
        details: {
          expectedType: rule.expectedType,
          dataRange: rule.dataRange,
          chartFound: true
        }
      }

    } catch (error) {
      log.error('图表验证错误:', error)
      return {
        success: false,
        message: `图表验证时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证数据透视表
   */
  private async validatePivotTable(rule: ValidationRule): Promise<ValidationResult> {
    try {
      // 更严格的透视表验证逻辑
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      // 1. 检查是否创建了新的工作表（透视表通常会创建新工作表）
      let hasNewWorksheet = false
      try {
        // 尝试获取工作表数量，如果有多个工作表说明可能创建了透视表
        const worksheetCount = workbook.getSheetCount ? workbook.getSheetCount() : 1
        if (worksheetCount > 1) {
          hasNewWorksheet = true
          log.debug(`检测到 ${worksheetCount} 个工作表，可能包含透视表`)
        }
      } catch (e) {
        log.debug('检查工作表数量失败:', e)
      }

      // 2. 检查DOM中是否存在透视表相关元素
      const pivotSelectors = [
        '.pivot-table',
        '.univer-pivot-table',
        '.pivot-container',
        '[data-pivot-id]',
        '.univer-pivot',
        '.pivot-field-list',
        '.univer-drawing-object', // Univer绘图对象可能包含透视表
        '.pivot-table-container',
        '.ms-pivot-table', // 可能的Microsoft样式
        '[class*="pivot"]' // 包含pivot的类名
      ]

      let pivotElementFound = false
      let foundSelector = ''
      for (const selector of pivotSelectors) {
        const elements = document.querySelectorAll(selector)
        if (elements.length > 0) {
          pivotElementFound = true
          foundSelector = selector
          log.debug(`找到透视表元素: ${selector}, 数量: ${elements.length}`)
          break
        }
      }

      // 3. 检查当前工作表中是否有透视表的特征数据结构
      let hasPivotStructure = false
      try {
        // 检查是否有类似透视表的数据结构（行标题、列标题、汇总数据）
        const range = worksheet.getRange('A1:Z50') // 检查较大范围
        const values = range.getValues()

        if (values && values.length > 0) {
          // 查找可能的透视表结构特征
          for (let i = 0; i < Math.min(values.length, 20); i++) {
            for (let j = 0; j < Math.min(values[i].length, 20); j++) {
              const cellValue = values[i][j]
              if (cellValue && typeof cellValue === 'string') {
                // 检查是否包含透视表常见的标识词
                const pivotKeywords = ['总计', '小计', '求和', '计数', '平均值', '最大值', '最小值', 'Sum', 'Count', 'Average', 'Total']
                if (pivotKeywords.some(keyword => cellValue.includes(keyword))) {
                  hasPivotStructure = true
                  log.debug(`在 ${String.fromCharCode(65 + j)}${i + 1} 找到透视表特征: ${cellValue}`)
                  break
                }
              }
            }
            if (hasPivotStructure) break
          }
        }
      } catch (e) {
        log.debug('检查透视表数据结构失败:', e)
      }

      // 4. 验证期望的字段是否存在（如果配置了expectedFields）
      const fieldsValidation = { found: true, details: '' }
      const strictValidation = { passed: true, details: '' }

      if (rule.expectedFields && rule.expectedFields.length > 0) {
        try {
          const range = worksheet.getRange('A1:Z50')
          const values = range.getValues()
          const foundFields: string[] = []

          // 在工作表中查找期望的字段
          for (const expectedField of rule.expectedFields) {
            let fieldFound = false
            for (let i = 0; i < Math.min(values.length, 20); i++) {
              for (let j = 0; j < Math.min(values[i].length, 20); j++) {
                const cellValue = values[i][j]
                if (cellValue && typeof cellValue === 'string' && cellValue.includes(expectedField)) {
                  fieldFound = true
                  foundFields.push(expectedField)
                  break
                }
              }
              if (fieldFound) break
            }
          }

          if (foundFields.length < rule.expectedFields.length) {
            fieldsValidation.found = false
            const missingFields = rule.expectedFields.filter(field => !foundFields.includes(field))
            fieldsValidation.details = `缺少字段: ${missingFields.join(', ')}`
          }

          // 5. 严格验证模式（如果启用）
          if (rule.strictValidation) {
            log.debug('启用严格验证模式')
            const validationErrors: string[] = []

            // 验证行标题
            if (rule.expectedRowHeaders && rule.expectedRowHeaders.length > 0) {
              const foundRowHeaders: string[] = []
              for (const expectedHeader of rule.expectedRowHeaders) {
                let headerFound = false
                for (let i = 0; i < Math.min(values.length, 20); i++) {
                  for (let j = 0; j < Math.min(values[i].length, 20); j++) {
                    const cellValue = values[i][j]
                    if (cellValue && typeof cellValue === 'string' && cellValue.includes(expectedHeader)) {
                      headerFound = true
                      foundRowHeaders.push(expectedHeader)
                      break
                    }
                  }
                  if (headerFound) break
                }
              }
              if (foundRowHeaders.length < rule.expectedRowHeaders.length) {
                const missingHeaders = rule.expectedRowHeaders.filter(header => !foundRowHeaders.includes(header))
                validationErrors.push(`缺少行标题: ${missingHeaders.join(', ')}`)
              }
            }

            // 验证列标题
            if (rule.expectedColumnHeaders && rule.expectedColumnHeaders.length > 0) {
              const foundColumnHeaders: string[] = []
              for (const expectedHeader of rule.expectedColumnHeaders) {
                let headerFound = false
                for (let i = 0; i < Math.min(values.length, 20); i++) {
                  for (let j = 0; j < Math.min(values[i].length, 20); j++) {
                    const cellValue = values[i][j]
                    if (cellValue && typeof cellValue === 'string' && cellValue.includes(expectedHeader)) {
                      headerFound = true
                      foundColumnHeaders.push(expectedHeader)
                      break
                    }
                  }
                  if (headerFound) break
                }
              }
              if (foundColumnHeaders.length < rule.expectedColumnHeaders.length) {
                const missingHeaders = rule.expectedColumnHeaders.filter(header => !foundColumnHeaders.includes(header))
                validationErrors.push(`缺少列标题: ${missingHeaders.join(', ')}`)
              }
            }

            // 验证总计值
            if (rule.expectedTotalValue !== undefined) {
              let totalValueFound = false
              for (let i = 0; i < Math.min(values.length, 20); i++) {
                for (let j = 0; j < Math.min(values[i].length, 20); j++) {
                  const cellValue = values[i][j]
                  if (typeof cellValue === 'number' && Math.abs(cellValue - rule.expectedTotalValue) < 0.01) {
                    totalValueFound = true
                    break
                  }
                }
                if (totalValueFound) break
              }
              if (!totalValueFound) {
                validationErrors.push(`未找到期望的总计值: ${rule.expectedTotalValue}`)
              }
            }

            if (validationErrors.length > 0) {
              strictValidation.passed = false
              strictValidation.details = validationErrors.join('; ')
            }
          }

        } catch (e) {
          log.debug('验证字段失败:', e)
        }
      }

      // 综合判断是否创建了透视表
      const pivotCreated = hasNewWorksheet || pivotElementFound || hasPivotStructure

      if (!pivotCreated) {
        return {
          success: false,
          message: '未检测到数据透视表。请按照操作步骤创建透视表：\n1. 选择数据范围A1:D6\n2. 右键点击选择"数据透视表"或通过"插入"菜单\n3. 确认数据范围并选择放置位置\n4. 点击"确定"创建透视表\n\n提示：透视表通常会创建新的工作表或在当前工作表中显示汇总数据',
          details: {
            expectedFields: rule.expectedFields,
            hasNewWorksheet,
            pivotElementFound,
            hasPivotStructure,
            foundSelector,
            hint: '请确保已经成功创建了数据透视表'
          }
        }
      }

      // 如果配置了字段验证但验证失败
      if (!fieldsValidation.found) {
        return {
          success: false,
          message: `透视表创建成功，但字段配置不完整。${fieldsValidation.details}\n\n请确保透视表包含以下字段：${rule.expectedFields?.join(', ')}`,
          details: {
            expectedFields: rule.expectedFields,
            pivotCreated: true,
            fieldsValidation,
            hint: '请检查透视表的字段配置'
          }
        }
      }

      // 如果启用了严格验证但验证失败
      if (rule.strictValidation && !strictValidation.passed) {
        return {
          success: false,
          message: `透视表创建成功，但不符合严格验证要求。${strictValidation.details}\n\n请确保透视表包含：\n- 行标题：${rule.expectedRowHeaders?.join(', ') || '无要求'}\n- 列标题：${rule.expectedColumnHeaders?.join(', ') || '无要求'}\n- 总计值：${rule.expectedTotalValue || '无要求'}`,
          details: {
            expectedFields: rule.expectedFields,
            expectedRowHeaders: rule.expectedRowHeaders,
            expectedColumnHeaders: rule.expectedColumnHeaders,
            expectedTotalValue: rule.expectedTotalValue,
            pivotCreated: true,
            fieldsValidation,
            strictValidation,
            hint: '请检查透视表的行标题、列标题和总计值是否正确'
          }
        }
      }

      return {
        success: true,
        message: rule.strictValidation ?
          '数据透视表创建成功！所有验证项目均通过，任务完成。' :
          '数据透视表创建成功！任务完成。',
        details: {
          expectedFields: rule.expectedFields,
          expectedRowHeaders: rule.expectedRowHeaders,
          expectedColumnHeaders: rule.expectedColumnHeaders,
          expectedTotalValue: rule.expectedTotalValue,
          pivotCreated: true,
          hasNewWorksheet,
          pivotElementFound,
          hasPivotStructure,
          foundSelector,
          fieldsValidation,
          strictValidation
        }
      }

    } catch (error) {
      log.error('透视表验证错误:', error)
      return {
        success: false,
        message: `透视表验证时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证筛选功能 - 基于官方FFilter API
   */
  private async validateFilter(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.dataRange) {
      return {
        success: false,
        message: '验证规则配置错误：缺少数据范围'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      log.validation('筛选验证 - 开始基于官方FFilter API的验证')

      // 使用官方FFilter API进行验证
      const filter = worksheet.getFilter()
      log.validation('筛选验证 - 获取筛选器:', filter)

      if (!filter) {
        return {
          success: false,
          message: '未检测到筛选器。请确保已正确执行以下步骤：\n\n1. 选择数据范围（包含表头）\n2. 启用筛选功能（数据 → 筛选）\n3. 设置筛选条件\n\n提示：筛选功能会在表头显示下拉箭头。',
          details: {
            filterDetected: false,
            hint: '请先启用筛选功能'
          }
        }
      }

      // 获取筛选范围
      const filterAPI = filter as FilterAPI
      const filterRange = filterAPI?.getRange?.()
      log.validation('筛选验证 - 筛选范围:', filterRange?.getA1Notation?.())

      // 获取被筛选掉的行
      const filteredOutRows = filterAPI?.getFilteredOutRows?.()
      log.validation('筛选验证 - 被筛选掉的行:', filteredOutRows)

      // 检查是否有筛选条件被设置
      let hasFilterCriteria = false
      const filterCriteriaDetails: Record<string, unknown> = {}

      if (filterRange && filterRange.getA1Notation) {
        const rangeNotation = filterRange.getA1Notation()
        const rangeMatch = rangeNotation?.match(/([A-Z]+)(\d+):([A-Z]+)(\d+)/)

        if (rangeMatch) {
          const startCol = rangeMatch[1]
          const endCol = rangeMatch[3]
          const startColIndex = this.getColumnIndex(startCol)
          const endColIndex = this.getColumnIndex(endCol)

          for (let colIndex = startColIndex; colIndex <= endColIndex; colIndex++) {
            try {
              const criteria = filterAPI?.getColumnFilterCriteria?.(colIndex)
              if (criteria) {
                hasFilterCriteria = true
                filterCriteriaDetails[`column_${colIndex}`] = criteria
                log.debug(`筛选验证 - 列${colIndex}的筛选条件:`, criteria)
              }
            } catch (error) {
              log.debug(`筛选验证 - 获取列${colIndex}筛选条件失败:`, error)
            }
          }
        }
      }

      // 验证筛选是否真正应用
      if (!hasFilterCriteria) {
        return {
          success: false,
          message: '未检测到筛选条件。请确保已正确设置筛选条件：\n\n1. 点击表头的下拉箭头\n2. 取消选中"全选"\n3. 只勾选需要显示的值\n4. 点击"确定"\n\n提示：设置筛选条件后，部分行会被隐藏。',
          details: {
            filterDetected: true,
            hasFilterCriteria: false,
            filterRange: filterRange?.getA1Notation?.(),
            hint: '筛选器已启用但未设置筛选条件'
          }
        }
      }

      // 验证筛选条件是否符合预期
      if (rule.expectedFilteredData && rule.expectedFilteredData.length > 0) {
        const criteriaValidation = await this.validateFilterCriteria(filterCriteriaDetails, rule)
        if (!criteriaValidation.isValid) {
          return {
            success: false,
            message: criteriaValidation.message || '筛选条件不符合预期',
            details: {
              filterDetected: true,
              hasFilterCriteria: true,
              filterCriteria: filterCriteriaDetails,
              criteriaValidation: criteriaValidation,
              hint: '筛选条件设置不正确'
            }
          }
        }
      }

      // 验证筛选结果
      if (filteredOutRows && filteredOutRows.length > 0) {
        log.validation('筛选验证 - 检测到筛选结果，有行被筛选掉')

        // 验证被筛选掉的行数是否合理
        if (rule.expectedVisibleRows !== undefined) {
          const range = worksheet.getRange(rule.dataRange)
          const values = range.getValues()
          const totalRows = values.length - 1 // 减去表头
          const expectedFilteredOutRows = totalRows - rule.expectedVisibleRows

          if (filteredOutRows.length !== expectedFilteredOutRows) {
            return {
              success: false,
              message: `筛选结果不正确。期望显示 ${rule.expectedVisibleRows} 行数据，但实际筛选掉了 ${filteredOutRows.length} 行（应该筛选掉 ${expectedFilteredOutRows} 行）。\n\n请检查筛选条件设置。`,
              details: {
                filterDetected: true,
                hasFilterCriteria: true,
                totalRows: totalRows,
                expectedVisibleRows: rule.expectedVisibleRows,
                expectedFilteredOutRows: expectedFilteredOutRows,
                actualFilteredOutRows: filteredOutRows.length,
                filteredOutRows: filteredOutRows,
                hint: '筛选结果行数不正确'
              }
            }
          }
        }
      } else {
        // 没有行被筛选掉，可能筛选条件包含了所有数据
        log.validation('筛选验证 - 没有行被筛选掉，检查筛选条件是否合理')

        if (rule.expectedVisibleRows !== undefined) {
          const range = worksheet.getRange(rule.dataRange)
          const values = range.getValues()
          const totalRows = values.length - 1 // 减去表头

          if (totalRows !== rule.expectedVisibleRows) {
            return {
              success: false,
              message: `筛选条件可能设置不正确。期望显示 ${rule.expectedVisibleRows} 行数据，但当前显示 ${totalRows} 行。\n\n请检查筛选条件是否正确设置。`,
              details: {
                filterDetected: true,
                hasFilterCriteria: true,
                totalRows: totalRows,
                expectedVisibleRows: rule.expectedVisibleRows,
                filteredOutRows: [],
                hint: '筛选条件可能包含了所有数据'
              }
            }
          }
        }
      }

      return {
        success: true,
        message: '筛选验证通过！筛选功能使用正确。',
        details: {
          filterDetected: true,
          hasFilterCriteria: true,
          filterRange: filterRange?.getA1Notation?.(),
          filterCriteria: filterCriteriaDetails,
          filteredOutRows: filteredOutRows || [],
          hint: '筛选功能使用正确'
        }
      }

    } catch (error) {
      log.error('筛选验证错误:', error)
      return {
        success: false,
        message: `验证筛选时发生错误: ${error}`,
        details: { error: String(error) }
      }
    }
  }

  /**
   * 验证筛选条件是否符合预期
   */
  private async validateFilterCriteria(filterCriteria: unknown, rule: ValidationRule): Promise<{ isValid: boolean; message?: string }> {
    try {
      if (!rule.expectedFilteredData || rule.expectedFilteredData.length === 0) {
        return { isValid: true }
      }

      // 分析期望数据的筛选模式
      const expectedPatterns: Record<number, Set<unknown>> = {}

      for (const row of rule.expectedFilteredData) {
        for (const [colKey, value] of Object.entries(row)) {
          const colIndex = parseInt(colKey)
          if (!expectedPatterns[colIndex]) {
            expectedPatterns[colIndex] = new Set()
          }
          expectedPatterns[colIndex].add(value)
        }
      }

      log.debug('筛选条件验证 - 期望的筛选模式:', expectedPatterns)
      log.debug('筛选条件验证 - 实际的筛选条件:', filterCriteria)

      // 检查筛选条件是否与期望模式匹配
      if (filterCriteria && typeof filterCriteria === 'object') {
        for (const [colKey, criteria] of Object.entries(filterCriteria)) {
          const colIndex = parseInt(colKey.split('_')[1])
          const expectedValues = expectedPatterns[colIndex]

          if (expectedValues && criteria && typeof criteria === 'object' &&
              'filters' in criteria && criteria.filters &&
              typeof criteria.filters === 'object' && 'filters' in criteria.filters) {
            const filtersObj = criteria.filters as { filters: unknown[] };
            const actualFilters = new Set(filtersObj.filters)
            const expectedFiltersArray = Array.from(expectedValues)

            // 检查筛选条件是否包含期望的值
            let hasExpectedValues = false
            for (const expectedValue of expectedFiltersArray) {
              if (actualFilters.has(String(expectedValue))) {
                hasExpectedValues = true
                break
              }
            }

            if (!hasExpectedValues) {
              return {
                isValid: false,
                message: `列${colIndex + 1}的筛选条件不正确。期望包含值: ${expectedFiltersArray.join(', ')}，实际筛选条件: ${Array.from(actualFilters).join(', ')}`
              }
            }
          }
        }
      }

      return { isValid: true }
    } catch (error) {
      log.debug('筛选条件验证失败:', error)
      return { isValid: true } // 验证失败时默认通过，避免误判
    }
  }

  /**
   * 获取列索引（A=0, B=1, C=2...）
   */
  private getColumnIndex(column: string): number {
    let result = 0
    for (let i = 0; i < column.length; i++) {
      result = result * 26 + (column.charCodeAt(i) - 'A'.charCodeAt(0) + 1)
    }
    return result - 1
  }

  /**
   * 比较两个值是否相等
   */
  private compareValues(actual: unknown, expected: unknown): boolean {
    // 处理数字比较
    if (typeof expected === 'number' && typeof actual === 'number') {
      return Math.abs(actual - expected) < 0.0001 // 浮点数比较
    }

    // 处理字符串比较（忽略大小写和前后空格）
    if (typeof expected === 'string' && typeof actual === 'string') {
      return actual.trim().toLowerCase() === expected.trim().toLowerCase()
    }

    // 其他类型直接比较
    return actual === expected
  }

  /**
   * 验证单列排序
   */
  private async validateSort(rule: ValidationRule): Promise<ValidationResult> {
    try {
      log.validation('开始验证单列排序:', rule)

      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) throw new Error('未获取到工作簿')
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) throw new Error('未获取到工作表')

      // 确定数据范围，如果没有指定则使用默认范围
      const dataRange = rule.dataRange || 'A1:C6'
      const range = worksheet.getRange(dataRange)
      const values = range.getValues()

      if (!values || values.length < 2) {
        return {
          success: false,
          message: '数据范围内没有足够的数据进行排序验证。请确保数据范围内有数据并且已经进行了排序操作。'
        }
      }

      log.debug('获取到的数据:', values)

      // 如果有期望的顺序，直接验证是否匹配（更可靠的方法）
      if (rule.expectedOrder && rule.expectedOrder.length > 0) {
        // 跳过表头，从第二行开始验证
        const dataRows = values.slice(1)
        const actualOrder = dataRows.map((row: unknown[]) => row[0]) // 第一列是标识列（姓名）

        log.debug('期望顺序:', rule.expectedOrder)
        log.debug('实际顺序:', actualOrder)

        const isOrderCorrect = this.checkExpectedOrder(actualOrder, rule.expectedOrder)

        if (!isOrderCorrect) {
          return {
            success: false,
            message: `排序结果与期望顺序不匹配。\n期望顺序: ${rule.expectedOrder.join(', ')}\n实际顺序: ${actualOrder.join(', ')}\n\n请按照以下步骤进行排序：\n1. 选择数据范围${dataRange}\n2. 点击"数据"选项卡\n3. 点击"排序"\n4. 选择按${rule.column || rule.sortColumn}列进行${rule.direction === 'desc' ? '降序' : '升序'}排序\n5. 点击"确定"`
          }
        }

        return {
          success: true,
          message: '排序验证通过！数据已按期望顺序排列。',
          details: {
            expectedOrder: rule.expectedOrder,
            actualOrder: actualOrder,
            sortColumn: rule.column || rule.sortColumn,
            direction: rule.direction || rule.sortDirection
          }
        }
      }

      // 如果没有期望顺序，则验证排序规则
      const sortColumn = rule.column || rule.sortColumn
      if (!sortColumn) {
        return { success: false, message: '未指定排序列或期望顺序' }
      }

      const columnIndex = this.getColumnIndex(sortColumn)
      const direction = rule.direction || rule.sortDirection || 'asc'

      // 跳过表头，从第二行开始验证
      const dataRows = values.slice(1)

      // 验证数据是否按指定顺序排序
      const isCorrectlySorted = this.checkSortOrder(dataRows, columnIndex, direction)

      if (!isCorrectlySorted) {
        return {
          success: false,
          message: `数据未按${sortColumn}列${direction === 'asc' ? '升序' : '降序'}排序。请重新进行排序操作。`
        }
      }

      return {
        success: true,
        message: '排序验证通过！',
        details: {
          sortColumn: sortColumn,
          direction: direction
        }
      }
    } catch (error) {
      log.error('排序验证失败:', error)
      return { success: false, message: `排序验证过程中发生错误: ${error}` }
    }
  }

  /**
   * 验证多列排序
   */
  private async validateMultiSort(rule: ValidationRule): Promise<ValidationResult> {
    try {
      log.validation('开始验证多列排序:', rule)

      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) throw new Error('未获取到工作簿')
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) throw new Error('未获取到工作表')

      // 确定数据范围
      const dataRange = rule.dataRange || 'A1:C7'
      const range = worksheet.getRange(dataRange)
      const values = range.getValues()

      if (!values || values.length < 2) {
        return {
          success: false,
          message: '数据范围内没有足够的数据进行排序验证。请确保数据范围内有数据并且已经进行了多列排序操作。'
        }
      }

      log.debug('获取到的数据:', values)

      if (!rule.sorts || rule.sorts.length === 0) {
        return { success: false, message: '未指定排序条件' }
      }

      // 如果有期望的顺序，直接验证是否匹配（更可靠的方法）
      if (rule.expectedOrder && rule.expectedOrder.length > 0) {
        // 跳过表头，从第二行开始验证
        const dataRows = values.slice(1)
        const actualOrder = dataRows.map((row: unknown[]) => row[0]) // 第一列是标识列（姓名）

        log.debug('期望顺序:', rule.expectedOrder)
        log.debug('实际顺序:', actualOrder)

        const isOrderCorrect = this.checkExpectedOrder(actualOrder, rule.expectedOrder)

        if (!isOrderCorrect) {
          const sortDesc = rule.sorts.map(s => `${s.column}列${s.direction === 'asc' ? '升序' : '降序'}`).join('，然后按')
          return {
            success: false,
            message: `多列排序结果与期望顺序不匹配。\n期望顺序: ${rule.expectedOrder.join(', ')}\n实际顺序: ${actualOrder.join(', ')}\n\n请按照以下步骤进行多列排序：\n1. 选择数据范围${dataRange}\n2. 点击"数据"选项卡\n3. 点击"排序"\n4. 设置主要排序条件：按${sortDesc}\n5. 点击"确定"`
          }
        }

        return {
          success: true,
          message: '多列排序验证通过！数据已按期望顺序排列。',
          details: {
            expectedOrder: rule.expectedOrder,
            actualOrder: actualOrder,
            sorts: rule.sorts
          }
        }
      }

      // 如果没有期望顺序，则验证排序规则
      // 跳过表头，从第二行开始验证
      const dataRows = values.slice(1)

      // 验证多列排序
      const isCorrectlySorted = this.checkMultiColumnSortOrder(dataRows, rule.sorts)

      if (!isCorrectlySorted) {
        const sortDesc = rule.sorts.map(s => `${s.column}列${s.direction === 'asc' ? '升序' : '降序'}`).join('，然后按')
        return {
          success: false,
          message: `数据未按指定条件排序: 按${sortDesc}。请重新进行多列排序操作。`
        }
      }

      return {
        success: true,
        message: '多列排序验证通过！',
        details: {
          sorts: rule.sorts
        }
      }
    } catch (error) {
      log.error('多列排序验证失败:', error)
      return { success: false, message: `多列排序验证过程中发生错误: ${error}` }
    }
  }

  /**
   * 检查单列排序顺序
   */
  private checkSortOrder(dataRows: unknown[][], columnIndex: number, direction: string): boolean {
    for (let i = 0; i < dataRows.length - 1; i++) {
      const current = dataRows[i][columnIndex] as string | number
      const next = dataRows[i + 1][columnIndex] as string | number

      if (direction === 'asc') {
        if (current > next) return false
      } else {
        if (current < next) return false
      }
    }
    return true
  }

  /**
   * 检查多列排序顺序
   */
  private checkMultiColumnSortOrder(dataRows: unknown[][], sorts: Array<{ column: string; direction: 'asc' | 'desc' }>): boolean {
    for (let i = 0; i < dataRows.length - 1; i++) {
      const current = dataRows[i]
      const next = dataRows[i + 1]

      for (const sort of sorts) {
        const columnIndex = this.getColumnIndex(sort.column)
        const currentValue = current[columnIndex] as string | number
        const nextValue = next[columnIndex] as string | number

        if (currentValue !== nextValue) {
          if (sort.direction === 'asc') {
            if (currentValue > nextValue) return false
          } else {
            if (currentValue < nextValue) return false
          }
          break // 如果当前排序条件已经决定了顺序，就不需要检查后续条件
        }
      }
    }
    return true
  }

  /**
   * 检查期望顺序
   */
  private checkExpectedOrder(actualOrder: unknown[], expectedOrder: unknown[]): boolean {
    if (actualOrder.length !== expectedOrder.length) {
      return false
    }

    for (let i = 0; i < actualOrder.length; i++) {
      if (actualOrder[i] !== expectedOrder[i]) {
        return false
      }
    }

    return true
  }

  /**
   * 验证多单元格对齐方式
   */
  private async validateMultiCellAlignment(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cells || !Array.isArray(rule.cells)) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格对齐配置'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      const validationResults = []

      // 验证每个单元格的对齐方式
      for (const cellConfig of rule.cells) {
        const { cell, expectedAlignment } = cellConfig

        if (!cell || !expectedAlignment) {
          validationResults.push({
            cell: cell || '未知',
            expected: expectedAlignment || '未知',
            actual: '配置错误',
            match: false
          })
          continue
        }

        try {
          const range = worksheet.getRange(cell)
          if (!range) {
            validationResults.push({
              cell,
              expected: expectedAlignment,
              actual: '无法获取单元格',
              match: false
            })
            continue
          }

          // 获取单元格数据和样式信息
          const cellData = range.getCellData()
          let style: UniverCellStyle = {}

          // 根据Univer文档，样式可能是ID引用或直接的样式对象
          if (cellData?.s) {
            if (typeof cellData.s === 'string') {
              // 如果是字符串，说明是样式ID，需要从工作簿样式表中获取
              const workbook = this.univerAPI.getActiveWorkbook()
              let styles = {}

              try {
                // 使用save()方法获取工作簿数据，按照文档建议
                try {
                  const workbookData = await (workbook as unknown as { save(): Promise<{ styles?: Record<string, UniverCellStyle> }> }).save()
                  styles = workbookData?.styles || {}
                  log.debug('使用save()获取样式表:', { totalStyles: Object.keys(styles).length })
                } catch (saveError) {
                  log.debug('save()方法失败，尝试其他方法:', saveError)

                  // 备用方法：尝试从Univer实例获取
                  const univerInstance = (this.univerAPI as unknown as { _univerInstance?: unknown })?._univerInstance
                  if (univerInstance) {
                    const currentWorkbook = (univerInstance as { getCurrentUniverSheetInstance(): unknown }).getCurrentUniverSheetInstance()
                    const workbookSnapshot = (currentWorkbook as { save?(): unknown; getSnapshot?(): unknown })?.save?.() || (currentWorkbook as { save?(): unknown; getSnapshot?(): unknown })?.getSnapshot?.()
                    styles = (workbookSnapshot as { styles?: Record<string, UniverCellStyle> })?.styles || {}
                  }
                }

                log.debug('获取到的样式表:', { totalStyles: Object.keys(styles).length, styleId: cellData.s })
              } catch (error) {
                log.debug('获取样式表失败:', error)
              }

              style = styles[cellData.s] || {}
              log.debug('从样式表获取样式:', { styleId: cellData.s, style, hasStyle: !!styles[cellData.s] })
            } else {
              // 如果是对象，直接使用
              style = cellData.s
              log.debug('直接使用样式对象:', style)
            }
          }

          // 从样式中获取水平对齐方式 (ht属性)
          const actualAlignment = style.ht

          log.validation('单元格对齐验证调试信息:', {
            cell,
            expectedAlignment,
            actualAlignment,
            actualAlignmentType: typeof actualAlignment,
            cellData,
            style
          })

          // 将Univer的对齐值转换为我们的标准值
          let normalizedActual = ''
          switch (actualAlignment) {
            case 1:
              normalizedActual = 'left'
              break
            case 2:
              normalizedActual = 'center'
              break
            case 3:
              normalizedActual = 'right'
              break
            default:
              // 如果没有设置对齐方式，默认为左对齐
              normalizedActual = 'left'
          }

          const isMatch = normalizedActual === expectedAlignment

          validationResults.push({
            cell,
            expected: expectedAlignment,
            actual: normalizedActual,
            match: isMatch
          })

        } catch (cellError) {
          log.error(`验证单元格 ${cell} 对齐方式时发生错误:`, cellError)
          validationResults.push({
            cell,
            expected: expectedAlignment,
            actual: '验证失败',
            match: false
          })
        }
      }

      const allMatch = validationResults.every(result => result.match)
      const failedValidations = validationResults.filter(result => !result.match)

      let message = ''
      if (allMatch) {
        message = '单元格对齐方式验证通过！'
      } else {
        const failedDetails = failedValidations.map(v =>
          `${v.cell}(期望:${v.expected}, 实际:${v.actual})`
        ).join('、')
        message = `单元格对齐方式不正确。失败的单元格: ${failedDetails}\n\n请按照以下步骤设置对齐方式：\n1. 选中需要设置的单元格\n2. 右键选择"设置单元格格式"\n3. 在"对齐"选项卡中选择相应的水平对齐方式\n4. 点击"确定"`
      }

      return {
        success: allMatch,
        message,
        details: {
          validationResults,
          totalCells: rule.cells.length,
          passedCells: validationResults.filter(r => r.match).length
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `验证单元格对齐方式时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证多边框设置
   */
  private async validateMultiBorder(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cells || !Array.isArray(rule.cells)) {
      return {
        success: false,
        message: '验证规则配置错误：缺少边框配置'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      const validationResults = []

      // 验证每个区域的边框设置
      for (const cellConfig of rule.cells) {
        const { range, cell, expectedBorder, expectedBorderColor, description } = cellConfig
        const targetRange = range || cell // 支持range或cell属性

        if (!targetRange || !expectedBorder) {
          validationResults.push({
            range: targetRange || '未知',
            expected: expectedBorder || '未知',
            actual: '配置错误',
            match: false,
            description: description || ''
          })
          continue
        }

        try {
          // 验证边框设置
          const borderValidation = await this.validateRangeBorder(
            worksheet,
            targetRange,
            expectedBorder,
            expectedBorderColor
          )

          validationResults.push({
            range: targetRange,
            expected: expectedBorder,
            actual: borderValidation.actualBorder,
            match: borderValidation.isValid,
            description: description || '',
            borderColor: borderValidation.actualBorderColor,
            expectedBorderColor: expectedBorderColor
          })

        } catch (cellError) {
          log.error(`验证范围 ${targetRange} 边框时发生错误:`, cellError)
          validationResults.push({
            range: targetRange,
            expected: expectedBorder,
            actual: '验证失败',
            match: false,
            description: description || ''
          })
        }
      }

      const allMatch = validationResults.every(result => result.match)
      const failedValidations = validationResults.filter(result => !result.match)

      let message = ''
      if (allMatch) {
        message = '边框设置验证通过！'
      } else {
        const failedDetails = failedValidations.map(v =>
          `${v.range}(期望:${v.expected}, 实际:${v.actual})`
        ).join('、')
        message = `边框设置不正确。失败的区域: ${failedDetails}\n\n请按照以下步骤设置边框：\n1. 选中需要设置的区域\n2. 右键选择"设置单元格格式"\n3. 在"边框"选项卡中选择相应的边框样式\n4. 如需设置边框颜色，请先选择颜色再设置边框\n5. 点击"确定"`
      }

      return {
        success: allMatch,
        message,
        details: {
          validationResults,
          totalRanges: rule.cells.length,
          passedRanges: validationResults.filter(r => r.match).length
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `验证边框设置时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证范围边框设置
   */
  private async validateRangeBorder(
    worksheet: UniverWorksheet,
    rangeAddress: string,
    expectedBorder: string,
    expectedBorderColor?: string
  ): Promise<{
    isValid: boolean;
    actualBorder: string;
    actualBorderColor?: string;
  }> {
    try {
      log.debug('验证范围边框:', { rangeAddress, expectedBorder, expectedBorderColor })

      const range = worksheet.getRange(rangeAddress)
      if (!range) {
        return {
          isValid: false,
          actualBorder: '无法获取范围'
        }
      }

      // 解析范围，获取所有单元格
      const cells = this.getCellsInRange(rangeAddress)
      log.debug('范围内的单元格:', cells)

      if (cells.length === 0) {
        return {
          isValid: false,
          actualBorder: '范围解析失败'
        }
      }

      // 根据期望的边框类型验证
      switch (expectedBorder) {
        case 'outline':
          return await this.validateOutlineBorder(worksheet, rangeAddress, cells, expectedBorderColor)
        case 'all':
          return await this.validateAllBorders(worksheet, rangeAddress, cells, expectedBorderColor)
        case 'thick':
          return await this.validateThickBorder(worksheet, rangeAddress, cells, expectedBorderColor)
        default:
          return {
            isValid: false,
            actualBorder: `不支持的边框类型: ${expectedBorder}`
          }
      }
    } catch (error) {
      log.error('验证范围边框失败:', error)
      return {
        isValid: false,
        actualBorder: '验证失败'
      }
    }
  }

  private async validateConditionalFormat(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      // 暂时简化验证逻辑，避免API调用错误
      // 实际应用中需要根据Univer的具体API文档来实现
      log.validation('开始验证条件格式任务:', rule)

      // 验证简单条件格式
      if (rule.type === 'conditionalFormat') {
        return await this.validateSimpleConditionalFormat(rule, worksheet)
      }

      // 验证多条件格式
      if (rule.type === 'multiConditionalFormat') {
        return await this.validateMultiConditionalFormat(rule, worksheet)
      }

      return {
        success: false,
        message: `不支持的条件格式验证类型: ${rule.type}`
      }
    } catch (error) {
      log.error('条件格式验证失败:', error)
      return {
        success: false,
        message: '条件格式验证过程中发生错误，请重试'
      }
    }
  }

  /**
   * 验证简单条件格式
   */
  private async validateSimpleConditionalFormat(rule: ValidationRule, worksheet: UniverWorksheet): Promise<ValidationResult> {
    const { range, condition, value, expectedFormattedCells, expectedBackgroundColor } = rule

    if (!range || !condition || !expectedFormattedCells || !expectedBackgroundColor) {
      return {
        success: false,
        message: '验证规则配置错误：缺少必要的条件格式参数'
      }
    }

    log.validation('验证简单条件格式:', { range, condition, value, expectedFormattedCells, expectedBackgroundColor })

    try {
      // 使用Univer API获取用户设置的条件格式规则
      const fWorksheet = worksheet

      // 获取指定范围的条件格式规则（只检查，不修改）
      const fRange = fWorksheet.getRange(range)
      const conditionalRules = fRange.getConditionalFormattingRules()
      log.debug('获取到的条件格式规则:', conditionalRules)

      // 如果没有检测到条件格式规则，说明用户还没有设置条件格式
      if (!conditionalRules || conditionalRules.length === 0) {
        log.debug('没有检测到条件格式规则，用户需要手动设置条件格式')

        // 不自动设置，只提示用户操作
        return {
          success: false,
          message: `请设置条件格式。\n\n操作步骤：\n1. 选择数据范围 ${range}\n2. 点击"数据"菜单中的"条件格式"\n3. 选择"突出显示单元格规则" → "大于"\n4. 输入条件值：${value}\n5. 选择红色背景色（在颜色选择器中选择第3行第3列的红色）\n6. 点击"确定"\n\n注意：验证的是背景色（#FF0000），不是文字色！`
        }
      }

      // 如果能获取到条件格式规则，进行规则验证
      if (conditionalRules && conditionalRules.length > 0) {
        log.debug('检测到条件格式规则，进行规则验证')

        // 验证条件格式规则是否正确
        const ruleValidation = this.validateConditionalFormattingRule(conditionalRules, {
          range,
          condition,
          value,
          expectedBackgroundColor
        })

        if (!ruleValidation.isValid) {
          return {
            success: false,
            message: `条件格式规则设置不正确：${ruleValidation.message}\n\n请确保：\n1. 条件类型为"大于"\n2. 条件值为 ${value}\n3. 背景色为红色（支持多种格式：${expectedBackgroundColor}、rgb(245,82,82)、#f05252等）\n4. 应用范围为 ${range}\n\n注意：验证的是背景色，不是文字色！颜色比较不区分大小写。`
          }
        }

        // 如果规则验证通过，返回成功
        log.debug('条件格式规则验证通过，任务完成')
        return {
          success: true,
          message: '条件格式验证成功！已正确设置条件格式规则。'
        }
      }

      // 无论是否能获取到规则，都要验证单元格的实际格式效果
      log.debug('验证单元格实际格式效果')

      if (expectedFormattedCells && expectedFormattedCells.length > 0) {
        const formattedCellsFound = []
        const unformattedCells = []

        for (const cellAddress of expectedFormattedCells) {
          const hasCorrectFormat = await this.checkCellBackgroundColor(worksheet, cellAddress, expectedBackgroundColor)
          log.debug(`单元格 ${cellAddress} 背景色检查结果:`, hasCorrectFormat)

          if (hasCorrectFormat) {
            formattedCellsFound.push(cellAddress)
          } else {
            unformattedCells.push(cellAddress)
          }
        }

        // 检查是否所有期望的单元格都有正确的格式
        if (unformattedCells.length > 0) {
          return {
            success: false,
            message: `条件格式应用不完整。以下单元格缺少红色背景格式：${unformattedCells.join(', ')}\n\n请确保：\n1. 选择数据范围 ${range}\n2. 设置条件：大于 ${value}\n3. 选择红色背景色（在颜色选择器中选择第3行第3列的红色）\n4. 点击"确定"应用格式\n\n注意：验证的是背景色（#FF0000），不是文字色！`
          }
        }

        // 检查是否有不应该格式化的单元格被格式化了
        const allCellsInRange = this.getCellsInRange(range)
        const unexpectedFormattedCells = []

        for (const cellAddress of allCellsInRange) {
          if (!expectedFormattedCells.includes(cellAddress)) {
            const hasFormat = await this.checkCellBackgroundColor(worksheet, cellAddress, expectedBackgroundColor)
            if (hasFormat) {
              unexpectedFormattedCells.push(cellAddress)
            }
          }
        }

        if (unexpectedFormattedCells.length > 0) {
          return {
            success: false,
            message: `条件格式设置错误。以下单元格不应该有红色背景格式：${unexpectedFormattedCells.join(', ')}\n\n请检查条件值是否设置正确（应该是 ${value}）。`
          }
        }

        log.debug('所有单元格格式验证通过')
        return {
          success: true,
          message: '条件格式验证成功！已正确设置条件格式规则。'
        }
      }

      return {
        success: false,
        message: `请设置条件格式。\n\n操作步骤：\n1. 选择数据范围 ${range}\n2. 点击"数据"菜单中的"条件格式"\n3. 选择"突出显示单元格规则" → "大于"\n4. 输入条件值：${value}\n5. 选择红色背景色（在颜色选择器中选择第3行第3列的红色）\n6. 点击"确定"\n\n注意：验证的是背景色（#FF0000），不是文字色！`
      }
    } catch (error) {
      log.error('验证简单条件格式时出错:', error)
      return {
        success: false,
        message: '验证过程中发生错误，请重试'
      }
    }
  }

  /**
   * 验证多条件格式
   */
  private async validateMultiConditionalFormat(rule: ValidationRule, worksheet: UniverWorksheet): Promise<ValidationResult> {
    const { range, conditions, expectedResults } = rule

    if (!range || !conditions || !expectedResults) {
      return {
        success: false,
        message: '验证规则配置错误：缺少必要的多条件格式参数'
      }
    }

    log.validation('验证多条件格式:', { range, conditions, expectedResults })

    try {
      // 使用Univer API获取用户设置的条件格式规则
      const fWorksheet = worksheet

      // 获取指定范围的条件格式规则（只检查，不修改）
      const fRange = fWorksheet.getRange(range)
      const conditionalRules = fRange.getConditionalFormattingRules()
      log.debug('获取到的多条件格式规则:', conditionalRules)

      // 如果没有检测到条件格式规则，说明用户还没有设置条件格式
      if (!conditionalRules || conditionalRules.length === 0) {
        log.debug('没有检测到条件格式规则，用户需要手动设置条件格式')

        // 不自动设置，只提示用户操作
        return {
          success: false,
          message: `请设置多条件格式。\n\n操作步骤：\n1. 选择数据范围 ${range}\n2. 设置第一个条件（成绩≥90）：\n   - 点击"数据"菜单中的"条件格式"\n   - 选择"突出显示单元格规则" → "大于或等于"\n   - 输入90，选择绿色背景\n   - 点击"确定"\n3. 设置第二个条件（成绩60-89）：\n   - 再次点击"条件格式" → "突出显示单元格规则" → "介于"\n   - 输入60到89，选择黄色背景\n   - 点击"确定"\n4. 设置第三个条件（成绩<60）：\n   - 点击"条件格式" → "突出显示单元格规则" → "小于"\n   - 输入60，选择红色背景\n   - 点击"确定"`
        }
      }

      // 如果能获取到条件格式规则，进行规则验证
      if (conditionalRules && conditionalRules.length > 0) {
        log.debug('检测到条件格式规则，进行规则验证')

        // 检查规则数量是否足够
        log.debug(`规则数量检查：期望 ${conditions.length} 个，实际 ${conditionalRules.length} 个`)
        if (conditionalRules.length < conditions.length) {
          log.debug('规则数量不足，返回错误')
          return {
            success: false,
            message: `条件格式规则数量不足。期望：${conditions.length}个规则，实际：${conditionalRules.length}个\n\n请确保设置了所有条件格式规则：\n${conditions.map((c: unknown, i: number) => `${i + 1}. ${this.getConditionDescription(c)}`).join('\n')}`
          }
        }

        log.debug('规则数量检查通过，开始验证每个规则')

        // 详细验证每个条件格式规则
        let validatedRules = 0

        for (let i = 0; i < conditions.length; i++) {
          const expectedCondition = conditions[i]
          log.debug(`验证第${i + 1}个期望条件:`, expectedCondition)

          // 在所有规则中查找匹配的规则
          let foundMatchingRule = false

          for (let j = 0; j < conditionalRules.length; j++) {
            const rule = conditionalRules[j]
            log.debug(`检查规则${j + 1}:`, rule)

            // 验证条件类型和值
            const conditionMatch = this.checkMultiRuleCondition(rule, expectedCondition)
            log.debug(`规则${j + 1}条件匹配:`, conditionMatch)

            // 验证背景色
            const colorMatch = this.checkMultiRuleBackgroundColor(rule, expectedCondition.color)
            log.debug(`规则${j + 1}颜色匹配:`, colorMatch)

            // 验证范围
            const rangeMatch = this.checkRuleRange(rule, range)
            log.debug(`规则${j + 1}范围匹配:`, rangeMatch)

            if (conditionMatch && colorMatch && rangeMatch) {
              log.debug(`✅ 找到匹配的规则${j + 1}，对应期望条件${i + 1}`)
              foundMatchingRule = true
              validatedRules++
              break
            }
          }

          if (!foundMatchingRule) {
            log.debug(`❌ 未找到匹配期望条件${i + 1}的规则`)
            return {
              success: false,
              message: `未找到匹配的条件格式规则：${this.getConditionDescription(expectedCondition)}\n\n请检查：\n1. 条件类型是否正确\n2. 条件值是否正确\n3. 背景色是否正确\n4. 应用范围是否正确`
            }
          }
        }

        log.debug(`验证完成：${validatedRules}/${conditions.length} 个条件匹配`)

        // 如果规则验证通过，返回成功
        log.debug('多条件格式规则验证通过，任务完成')
        return {
          success: true,
          message: '多条件格式验证成功！已正确设置所有条件格式规则。'
        }
      }

      // 无论是否能获取到规则，都要验证单元格的实际格式效果
      log.debug('验证单元格实际格式效果')

      // 验证期望的单元格是否有正确的背景色
      let correctCells = 0
      let totalExpectedCells = 0

      for (const [expectedColor, cellAddresses] of Object.entries(expectedResults)) {
        totalExpectedCells += (cellAddresses as string[]).length

        for (const cellAddress of cellAddresses as string[]) {
          const hasCorrectFormat = await this.checkCellBackgroundColor(worksheet, cellAddress, expectedColor)
          if (hasCorrectFormat) {
            correctCells++
            log.debug(`✅ ${cellAddress} 背景色正确: ${expectedColor}`)
          } else {
            log.debug(`❌ ${cellAddress} 背景色不正确，期望: ${expectedColor}`)
          }
        }
      }

      // 检查是否有不应该格式化的单元格被格式化了
      const allCellsInRange = this.getCellsInRange(range)
      const expectedFormattedCells = Object.values(expectedResults).flat() as string[]
      const unexpectedFormattedCells = []

      for (const cellAddress of allCellsInRange) {
        if (!expectedFormattedCells.includes(cellAddress)) {
          // 检查是否有任何条件格式颜色
          let hasUnexpectedFormat = false
          for (const expectedColor of Object.keys(expectedResults)) {
            if (await this.checkCellBackgroundColor(worksheet, cellAddress, expectedColor)) {
              hasUnexpectedFormat = true
              break
            }
          }
          if (hasUnexpectedFormat) {
            unexpectedFormattedCells.push(cellAddress)
          }
        }
      }

      if (unexpectedFormattedCells.length > 0) {
        return {
          success: false,
          message: `条件格式设置错误。以下单元格不应该有背景格式：${unexpectedFormattedCells.join(', ')}\n\n请检查条件值是否设置正确。`
        }
      }

      if (correctCells === totalExpectedCells) {
        log.debug('所有单元格格式验证通过')
        return {
          success: true,
          message: '多条件格式验证成功！已正确设置所有条件格式规则。'
        }
      }

      return {
        success: false,
        message: `请设置多条件格式。\n\n操作步骤：\n1. 选择数据范围 ${range}\n2. 设置第一个条件（成绩≥90）：绿色背景\n3. 设置第二个条件（成绩60-89）：黄色背景\n4. 设置第三个条件（成绩<60）：红色背景\n\n期望结果：\n${Object.entries(expectedResults).map(([color, cells]) => `${color}: ${(cells as string[]).join(', ')}`).join('\n')}`
      }
    } catch (error) {
      log.error('验证多条件格式时出错:', error)
      return {
        success: false,
        message: '验证过程中发生错误，请重试'
      }
    }
  }

  /**
   * 验证条件格式规则是否符合要求
   */
  private validateConditionalFormattingRule(rules: unknown[], expected: {
    range: string
    condition: string
    value: unknown
    expectedBackgroundColor: string
  }): { isValid: boolean; message: string } {
    try {
      log.debug('验证条件格式规则:', { rules, expected })

      // 检查是否有符合条件的规则
      for (const rule of rules) {
        log.debug('检查规则:', rule)

        // 检查规则类型
        if (this.checkRuleCondition(rule, expected.condition, expected.value)) {
          // 检查背景色
          if (this.checkRuleBackgroundColor(rule, expected.expectedBackgroundColor)) {
            // 检查应用范围
            if (this.checkRuleRange(rule, expected.range)) {
              return { isValid: true, message: '条件格式规则验证通过' }
            } else {
              return { isValid: false, message: `应用范围不正确，期望：${expected.range}` }
            }
          } else {
            return { isValid: false, message: `背景色不正确，期望：${expected.expectedBackgroundColor}` }
          }
        }
      }

      return { isValid: false, message: `未找到匹配的条件格式规则，期望条件：${expected.condition}，值：${expected.value}` }
    } catch (error) {
      log.error('验证条件格式规则失败:', error)
      return { isValid: false, message: '验证条件格式规则时发生错误' }
    }
  }

  /**
   * 检查规则条件是否匹配
   */
  private checkRuleCondition(rule: unknown, expectedCondition: string, expectedValue: unknown): boolean {
    try {
      log.debug('检查规则条件:', { rule, expectedCondition, expectedValue })

      // 根据实际的Univer API结构验证条件格式规则
      const ruleObj = rule as { rule?: unknown }
      if (rule && ruleObj.rule) {
        const cfRule = ruleObj.rule as { type?: string; value?: unknown; operator?: string }

        // 检查条件类型
        if (expectedCondition === 'greaterThan') {
          if (cfRule.operator === 'greaterThan' && cfRule.type === 'highlightCell') {
            // 检查条件值
            if (cfRule.value === expectedValue) {
              log.debug('条件格式规则条件验证通过')
              return true
            } else {
              log.debug(`条件值不匹配，期望：${expectedValue}，实际：${cfRule.value}`)
            }
          } else {
            log.debug(`条件类型不匹配，期望：greaterThan，实际：${cfRule.operator}`)
          }
        }
      } else {
        log.debug('规则结构不正确')
      }

      return false
    } catch (error) {
      log.error('检查规则条件失败:', error)
      return false
    }
  }

  /**
   * 检查规则背景色是否匹配
   */
  private checkRuleBackgroundColor(rule: unknown, expectedColor: string): boolean {
    try {
      log.debug('检查规则背景色:', { rule, expectedColor })

      // 根据实际的Univer API结构验证背景色
      const ruleObj = rule as { rule?: { style?: { bg?: { rgb?: string } } } }
      if (rule && ruleObj.rule?.style?.bg) {
        const bgColor = ruleObj.rule.style.bg.rgb
        log.debug(`背景色比较：期望 ${expectedColor}，实际 ${bgColor}`)

        // 支持多种颜色格式的比较，忽略大小写
        const normalizedExpected = expectedColor.toUpperCase()
        const normalizedActual = bgColor ? bgColor.toUpperCase() : ''

        // 检查是否匹配期望的颜色（支持多种格式）
        const isMatchingColor = this.isColorMatch(normalizedActual, normalizedExpected)

        if (isMatchingColor) {
          log.debug('背景色验证通过')
          return true
        } else {
          log.debug('背景色不匹配')
        }
      } else {
        log.debug('未找到背景色样式')
      }

      return false
    } catch (error) {
      log.error('检查规则背景色失败:', error)
      return false
    }
  }

  /**
   * 检查规则应用范围是否匹配
   */
  private checkRuleRange(rule: unknown, expectedRange: string): boolean {
    try {
      log.debug('检查规则范围:', { rule, expectedRange })

      // 检查模拟规则的范围（直接字符串比较）
      const ruleObj = rule as { range?: string }
      if (rule && ruleObj.range) {
        log.debug('模拟规则范围:', ruleObj.range)
        const rangeMatch = ruleObj.range === expectedRange
        log.debug(`范围匹配结果: ${ruleObj.range} === ${expectedRange} = ${rangeMatch}`)
        return rangeMatch
      }

      // 根据实际的Univer API结构验证范围
      const ruleWithRanges = rule as { ranges?: unknown[] }
      if (rule && ruleWithRanges.ranges && ruleWithRanges.ranges.length > 0) {
        const range = ruleWithRanges.ranges[0] as {
          startRow?: number;
          startColumn?: number;
          endRow?: number;
          endColumn?: number;
        }
        log.debug('API规则范围:', range)

        // 解析期望范围
        const expectedRangeInfo = this.parseRange(expectedRange)
        if (!expectedRangeInfo) {
          log.debug(`无法解析期望范围: ${expectedRange}`)
          return false
        }

        // 验证范围是否匹配
        const rangeMatch = range.startRow === expectedRangeInfo.startRow &&
                          range.startColumn === expectedRangeInfo.startColumn &&
                          range.endRow === expectedRangeInfo.endRow &&
                          range.endColumn === expectedRangeInfo.endColumn

        if (rangeMatch) {
          log.debug(`API范围验证通过: 行${range.startRow}-${range.endRow}，列${range.startColumn}-${range.endColumn}`)
          return true
        } else {
          log.debug(`API范围不匹配，期望：${expectedRange}(行${expectedRangeInfo.startRow}-${expectedRangeInfo.endRow}，列${expectedRangeInfo.startColumn}-${expectedRangeInfo.endColumn})，实际：行${range.startRow}-${range.endRow}，列${range.startColumn}-${range.endColumn}`)
        }
      } else {
        log.debug('未找到范围信息')
      }

      return false
    } catch (error) {
      log.error('检查规则范围失败:', error)
      return false
    }
  }

  /**
   * 检查范围是否匹配
   */
  private rangeMatches(_actualRange: unknown, _expectedRange: string): boolean {
    try {
      // 将期望范围转换为标准格式进行比较
      const expected = this.parseRange(_expectedRange)

      if (_actualRange && typeof _actualRange === 'object' &&
          'startRow' in _actualRange && 'endRow' in _actualRange &&
          'startColumn' in _actualRange && 'endColumn' in _actualRange) {
        const actualRange = _actualRange as { startRow: number; endRow: number; startColumn: number; endColumn: number };
        if (actualRange.startRow === expected.startRow &&
            actualRange.endRow === expected.endRow &&
            actualRange.startColumn === expected.startColumn &&
            actualRange.endColumn === expected.endColumn) {
          return true
        }
      }

      return false
    } catch (error) {
      log.error('检查范围匹配失败:', error)
      return false
    }
  }

  /**
   * 检查单元格背景色
   */
  private async checkCellBackgroundColor(worksheet: unknown, cellAddress: string, expectedColor: string): Promise<boolean> {
    try {
      log.debug(`检查单元格 ${cellAddress} 的背景色，期望：${expectedColor}`)

      // 解析单元格地址
      const { row, col } = this.parseCellAddress(cellAddress)
      log.debug(`解析单元格地址 ${cellAddress} -> 行:${row}, 列:${col}`)

      // 获取单元格范围 - 使用Univer的FRange API
      const worksheetObj = worksheet as { getRange?: (address: string) => unknown }
      const range = worksheetObj.getRange?.(cellAddress)
      log.debug('获取到的范围对象:', range)

      // 尝试多种方法获取背景色
      let actualColor = null

      // 定义 range 的类型
      const rangeObj = range as {
        getBackgrounds?: () => string[][];
        getBackground?: () => string;
        getValues?: () => unknown[][];
        getCellData?: () => { s?: { bg?: { rgb?: string } } };
      }

      // 方法1: 尝试getBackgrounds()
      try {
        const backgrounds = rangeObj.getBackgrounds?.()
        log.debug('getBackgrounds()结果:', backgrounds)
        if (backgrounds && backgrounds.length > 0 && backgrounds[0].length > 0) {
          actualColor = backgrounds[0][0]
        }
      } catch (e) {
        log.debug('getBackgrounds()方法失败:', e)
      }

      // 方法2: 尝试getBackground()
      if (!actualColor) {
        try {
          actualColor = rangeObj.getBackground?.()
          log.debug('getBackground()结果:', actualColor)
        } catch (e) {
          log.debug('getBackground()方法失败:', e)
        }
      }

      // 方法3: 尝试获取样式信息
      if (!actualColor) {
        try {
          const values = rangeObj.getValues?.()
          log.debug('getValues()结果:', values)

          // 尝试获取单元格的样式数据
          const cellData = rangeObj.getCellData?.()
          log.debug('getCellData()结果:', cellData)

          if (cellData && cellData.s && cellData.s.bg) {
            actualColor = cellData.s.bg.rgb || cellData.s.bg
          }
        } catch (e) {
          log.debug('获取样式信息失败:', e)
        }
      }

      log.debug(`单元格 ${cellAddress} 的实际背景色：${actualColor}`)

      if (actualColor) {
        // 比较颜色（考虑不同的颜色格式）
        const colorStr = typeof actualColor === 'string' ? actualColor : String(actualColor)
        const isMatch = this.colorsMatch(colorStr, expectedColor)
        log.debug(`颜色匹配结果: ${actualColor} vs ${expectedColor} = ${isMatch}`)
        return isMatch
      }

      // 如果无法获取背景色，检查是否是默认颜色
      log.debug(`无法获取单元格 ${cellAddress} 的背景色，假设为默认白色`)
      return this.colorsMatch('#ffffff', expectedColor) || this.colorsMatch('white', expectedColor)

    } catch (error) {
      log.error(`检查单元格 ${cellAddress} 背景色失败:`, error)
      return false
    }
  }

  /**
   * 获取范围内的所有单元格地址
   */
  private getCellsInRange(range: string): string[] {
    try {
      log.debug(`获取范围 ${range} 内的所有单元格`)

      const parsed = this.parseRange(range)
      const cells: string[] = []

      for (let row = parsed.startRow; row <= parsed.endRow; row++) {
        for (let col = parsed.startColumn; col <= parsed.endColumn; col++) {
          cells.push(this.getCellAddress(row, col))
        }
      }

      log.debug(`范围 ${range} 包含单元格:`, cells)
      return cells
    } catch (error) {
      log.error(`获取范围 ${range} 内单元格失败:`, error)
      return []
    }
  }

  /**
   * 解析范围字符串
   */
  private parseRange(range: string): { startRow: number; endRow: number; startColumn: number; endColumn: number } {
    try {
      // 解析类似 "C2:C6" 的范围
      const [startCell, endCell] = range.split(':')
      const start = this.parseCellAddress(startCell)
      const end = this.parseCellAddress(endCell)

      return {
        startRow: start.row,
        endRow: end.row,
        startColumn: start.col,
        endColumn: end.col
      }
    } catch (error) {
      log.error(`解析范围 ${range} 失败:`, error)
      throw error
    }
  }

  /**
   * 解析单元格地址
   */
  private parseCellAddress(cellAddress: string): { row: number; col: number } {
    try {
      // 解析类似 "C3" 的单元格地址
      const match = cellAddress.match(/^([A-Z]+)(\d+)$/)
      if (!match) {
        throw new Error(`无效的单元格地址: ${cellAddress}`)
      }

      const colStr = match[1]
      const rowStr = match[2]

      // 将列字母转换为数字（A=0, B=1, C=2, ...）
      let col = 0
      for (let i = 0; i < colStr.length; i++) {
        col = col * 26 + (colStr.charCodeAt(i) - 'A'.charCodeAt(0) + 1)
      }
      col -= 1 // 转换为0基索引

      // 将行号转换为数字（1基转0基）
      const row = parseInt(rowStr) - 1

      return { row, col }
    } catch (error) {
      log.error(`解析单元格地址 ${cellAddress} 失败:`, error)
      throw error
    }
  }

  /**
   * 获取单元格地址
   */
  private getCellAddress(row: number, col: number): string {
    try {
      // 将数字转换为列字母
      let colStr = ''
      let colNum = col + 1 // 转换为1基索引
      while (colNum > 0) {
        colNum -= 1
        colStr = String.fromCharCode('A'.charCodeAt(0) + (colNum % 26)) + colStr
        colNum = Math.floor(colNum / 26)
      }

      // 将行号转换为字符串（0基转1基）
      const rowStr = (row + 1).toString()

      return colStr + rowStr
    } catch (error) {
      log.error(`获取单元格地址失败:`, error)
      return ''
    }
  }

  /**
   * 比较两个颜色是否匹配
   */
  private colorsMatch(color1: string, color2: string): boolean {
    try {
      // 标准化颜色格式
      const normalized1 = this.normalizeColor(color1)
      const normalized2 = this.normalizeColor(color2)

      return normalized1 === normalized2
    } catch (error) {
      log.error('比较颜色失败:', error)
      return false
    }
  }

  /**
   * 标准化颜色格式
   */
  private normalizeColor(color: string): string {
    try {
      if (!color) return '#ffffff'

      // 移除空格并转换为小写
      color = color.trim().toLowerCase()

      // 如果是十六进制颜色，确保有#前缀
      if (color.match(/^[0-9a-f]{6}$/)) {
        return '#' + color
      }

      // 如果已经有#前缀，直接返回
      if (color.startsWith('#')) {
        return color
      }

      // 处理常见颜色名称
      const colorMap: { [key: string]: string } = {
        'red': '#ff0000',
        'green': '#00ff00',
        'blue': '#0000ff',
        'yellow': '#ffff00',
        'white': '#ffffff',
        'black': '#000000'
      }

      return colorMap[color] || color
    } catch (error) {
      log.error('标准化颜色失败:', error)
      return color
    }
  }

  /**
   * 测试条件格式API，通过编程方式设置条件格式
   */
  private async testConditionalFormatAPI(_worksheet: unknown, _range: string, _condition: string, _value: unknown, _expectedBackgroundColor: string): Promise<void> {
    try {
      log.debug('开始测试条件格式API设置...')
      // 这是一个测试方法，暂时不实现具体逻辑
      log.debug('参数:', { _worksheet, _range, _condition, _value, _expectedBackgroundColor })
    } catch (_error) {
      log.error('测试条件格式API失败:', _error)
    }
  }


  /**
   * 获取条件描述
   */
  private getConditionDescription(condition: unknown): string {
    try {
      const conditionObj = condition as {
        type?: string;
        value?: unknown;
        minValue?: unknown;
        maxValue?: unknown;
        color?: string;
      }
      switch (conditionObj.type) {
        case 'greaterThanOrEqual':
          return `成绩≥${conditionObj.value}：${conditionObj.color}`
        case 'between':
          return `成绩${conditionObj.minValue}-${conditionObj.maxValue}：${conditionObj.color}`
        case 'lessThan':
          return `成绩<${conditionObj.value}：${conditionObj.color}`
        default:
          return `条件类型：${conditionObj.type}，值：${conditionObj.value}，颜色：${conditionObj.color}`
      }
    } catch {
      return '条件描述解析失败'
    }
  }

  /**
   * 验证多条件格式规则
   */
  private validateMultiConditionalRule(_rules: unknown[], _expectedCondition: unknown, _range: string): { isValid: boolean; message: string } {
    try {
      log.debug('验证多条件格式规则:', { _rules, _expectedCondition, _range })
      // 这是一个测试方法，暂时返回默认结果
      return { isValid: false, message: '未实现的方法' }
    } catch (error) {
      log.error('验证多条件格式规则失败:', error)
      return { isValid: false, message: '规则验证过程中发生错误' }
    }
  }

  /**
   * 检查多条件格式规则的条件
   */
  private checkMultiRuleCondition(rule: unknown, expectedCondition: unknown): boolean {
    try {
      const ruleObj = rule as { rule?: {
        operator?: string;
        value?: unknown;
        val?: unknown;
        minValue?: unknown;
        maxValue?: unknown;
        min?: unknown;
        max?: unknown;
        value1?: unknown;
        value2?: unknown;
        values?: unknown[];
      } }
      if (!rule || !ruleObj.rule) {
        log.debug('规则结构无效:', rule)
        return false
      }

      const cfRule = ruleObj.rule
      log.debug('检查条件匹配:', {
        expected: expectedCondition,
        actual: {
          operator: cfRule.operator,
          value: cfRule.value,
          minValue: cfRule.minValue,
          maxValue: cfRule.maxValue,
          fullRule: cfRule
        }
      })

      const expectedCondObj = expectedCondition as { type?: string; value?: unknown; minValue?: unknown; maxValue?: unknown }
      switch (expectedCondObj.type) {
        case 'greaterThanOrEqual': {
          // 支持多种可能的操作符格式
          const isGreaterThanOrEqualOperator = cfRule.operator === 'greaterThanOrEqual' ||
                                              cfRule.operator === 'greater_than_or_equal' ||
                                              cfRule.operator === 'gte' ||
                                              cfRule.operator === 'greaterThan'  // 有时API可能返回greaterThan

          const greaterActualValue = cfRule.value || cfRule.val
          const greaterValueMatch = Number(greaterActualValue) === Number(expectedCondObj.value)

          const greaterMatch = isGreaterThanOrEqualOperator && greaterValueMatch

          log.debug(`大于等于条件匹配: ${greaterMatch}`, {
            operatorMatch: isGreaterThanOrEqualOperator,
            valueMatch: greaterValueMatch,
            actualOperator: cfRule.operator,
            actualValue: greaterActualValue,
            expectedValue: expectedCondObj.value,
            fullRule: cfRule
          })
          return greaterMatch
        }
        case 'between': {
          // 支持多种可能的操作符格式
          const isBetweenOperator = cfRule.operator === 'between' ||
                                   cfRule.operator === 'betweenAnd' ||
                                   cfRule.operator === 'between_and'

          // 支持多种可能的值字段格式
          let actualMinValue, actualMaxValue

          // 如果value是数组，取数组的第一个和第二个元素
          if (Array.isArray(cfRule.value) && cfRule.value.length >= 2) {
            actualMinValue = cfRule.value[0]
            actualMaxValue = cfRule.value[1]
          } else {
            // 否则尝试其他字段
            actualMinValue = cfRule.minValue || cfRule.min || cfRule.value1 || cfRule.values?.[0]
            actualMaxValue = cfRule.maxValue || cfRule.max || cfRule.value2 || cfRule.values?.[1]
          }

          const minValueMatch = Number(actualMinValue) === Number(expectedCondObj.minValue)
          const maxValueMatch = Number(actualMaxValue) === Number(expectedCondObj.maxValue)

          const betweenMatch = isBetweenOperator && minValueMatch && maxValueMatch

          log.debug(`介于条件匹配: ${betweenMatch}`, {
            operatorMatch: isBetweenOperator,
            minValueMatch,
            maxValueMatch,
            actualOperator: cfRule.operator,
            actualMinValue,
            actualMaxValue,
            expectedMinValue: expectedCondObj.minValue,
            expectedMaxValue: expectedCondObj.maxValue,
            fullRule: cfRule
          })
          return betweenMatch
        }
        case 'lessThan': {
          // 支持多种可能的操作符格式
          const isLessThanOperator = cfRule.operator === 'lessThan' ||
                                    cfRule.operator === 'less_than' ||
                                    cfRule.operator === 'lt'

          const lessActualValue = cfRule.value || cfRule.val
          const lessValueMatch = Number(lessActualValue) === Number(expectedCondObj.value)

          const lessMatch = isLessThanOperator && lessValueMatch

          log.debug(`小于条件匹配: ${lessMatch}`, {
            operatorMatch: isLessThanOperator,
            valueMatch: lessValueMatch,
            actualOperator: cfRule.operator,
            actualValue: lessActualValue,
            expectedValue: expectedCondObj.value,
            fullRule: cfRule
          })
          return lessMatch
        }
        default:
          log.debug(`未知条件类型: ${expectedCondObj.type}`)
          return false
      }
    } catch (error) {
      log.error('检查多条件格式规则条件出错:', error)
      return false
    }
  }

  /**
   * 检查是否为货币格式
   */
  private isCurrencyFormat(format: string): boolean {
    if (!format) return false
    const currencySymbols = ['¥', '$', '€', '£', '￥', 'CNY', 'USD', 'EUR', 'GBP']
    return currencySymbols.some(symbol => format.includes(symbol))
  }

  /**
   * 检查是否为百分比格式
   */
  private isPercentageFormat(format: string): boolean {
    if (!format) return false
    return format.includes('%')
  }

  /**
   * 检查是否为日期格式
   */
  private isDateFormat(format: string): boolean {
    if (!format) return false
    const datePatterns = ['yyyy', 'mm', 'dd', 'yy', 'MM', 'DD', 'YYYY', 'MM/DD', 'DD/MM', 'YYYY-MM-DD']
    return datePatterns.some(pattern => format.includes(pattern))
  }



  /**
   * 检查多条件格式规则的背景色
   */
  private checkMultiRuleBackgroundColor(rule: unknown, expectedColor: string): boolean {
    try {
      const ruleObj = rule as { rule?: { style?: { bg?: { rgb?: string } } } }
      if (!ruleObj || !ruleObj.rule || !ruleObj.rule.style || !ruleObj.rule.style.bg) return false

      const bgColor = ruleObj.rule.style.bg.rgb
      log.debug(`多条件背景色比较：期望 ${expectedColor}，实际 ${bgColor}`)

      // 支持多种颜色格式的比较，忽略大小写
      const normalizedExpected = expectedColor.toUpperCase()
      const normalizedActual = bgColor ? bgColor.toUpperCase() : ''

      // 检查是否匹配指定颜色
      return this.isColorMatch(normalizedActual, normalizedExpected)
    } catch (error) {
      log.error('检查多条件格式规则背景色失败:', error)
      return false
    }
  }

  /**
   * 检查颜色是否匹配
   */
  private isColorMatch(actualColor: string, expectedColor: string): boolean {
    // 标准化颜色格式
    const normalizeColor = (color: string): string => {
      if (color.startsWith('RGB(')) {
        // 将 RGB(245,82,82) 转换为 #F55252
        const match = color.match(/RGB\((\d+),(\d+),(\d+)\)/)
        if (match) {
          const r = parseInt(match[1]).toString(16).padStart(2, '0')
          const g = parseInt(match[2]).toString(16).padStart(2, '0')
          const b = parseInt(match[3]).toString(16).padStart(2, '0')
          return `#${r}${g}${b}`.toUpperCase()
        }
      }
      return color.toUpperCase()
    }

    const normalizedActual = normalizeColor(actualColor)
    const normalizedExpected = normalizeColor(expectedColor)

    // 定义颜色映射表
    const colorMappings: Record<string, string[]> = {
      // 红色系列 (#f05252)
      '#F05252': ['#F05252', 'RGB(240,82,82)', '#F52', 'RED'],
      // 绿色系列 (#0da471)
      '#0DA471': ['#0DA471', 'RGB(13,164,113)', '#0A4', 'GREEN'],
      // 黄色系列 (#fac815)
      '#FAC815': ['#FAC815', 'RGB(250,200,21)', '#FC1', 'YELLOW'],
      // 兼容旧的红色值
      '#FF0000': ['#FF0000', 'RGB(255,0,0)', '#F00', 'RED']
    }

    // 检查直接匹配
    if (normalizedActual === normalizedExpected) {
      return true
    }

    // 检查颜色映射
    for (const [, variants] of Object.entries(colorMappings)) {
      if (variants.includes(normalizedExpected) && variants.includes(normalizedActual)) {
        return true
      }
    }

    return false
  }

  /**
   * 验证外边框
   */
  private async validateOutlineBorder(
    worksheet: UniverWorksheet,
    rangeAddress: string,
    cells: string[],
    expectedBorderColor?: string
  ): Promise<{ isValid: boolean; actualBorder: string; actualBorderColor?: string }> {
    try {
      log.debug('验证外边框:', { rangeAddress, cells, expectedBorderColor })

      // 解析范围
      const parsed = this.parseRange(rangeAddress)
      const { startRow, endRow, startColumn, endColumn } = parsed

      // 检查边界单元格是否有边框
      const borderChecks = []

      // 检查顶部边框
      for (let col = startColumn; col <= endColumn; col++) {
        const cellAddress = this.getCellAddress(startRow, col)
        const hasBorder = await this.checkCellBorder(worksheet, cellAddress, 'top', expectedBorderColor)
        borderChecks.push({ cell: cellAddress, position: 'top', hasBorder })
      }

      // 检查底部边框
      for (let col = startColumn; col <= endColumn; col++) {
        const cellAddress = this.getCellAddress(endRow, col)
        const hasBorder = await this.checkCellBorder(worksheet, cellAddress, 'bottom', expectedBorderColor)
        borderChecks.push({ cell: cellAddress, position: 'bottom', hasBorder })
      }

      // 检查左边框
      for (let row = startRow; row <= endRow; row++) {
        const cellAddress = this.getCellAddress(row, startColumn)
        const hasBorder = await this.checkCellBorder(worksheet, cellAddress, 'left', expectedBorderColor)
        borderChecks.push({ cell: cellAddress, position: 'left', hasBorder })
      }

      // 检查右边框
      for (let row = startRow; row <= endRow; row++) {
        const cellAddress = this.getCellAddress(row, endColumn)
        const hasBorder = await this.checkCellBorder(worksheet, cellAddress, 'right', expectedBorderColor)
        borderChecks.push({ cell: cellAddress, position: 'right', hasBorder })
      }

      const allBordersPresent = borderChecks.every(check => check.hasBorder)

      log.debug('外边框检查结果:', { borderChecks, allBordersPresent })

      return {
        isValid: allBordersPresent,
        actualBorder: allBordersPresent ? 'outline' : '部分或无外边框'
      }
    } catch (error) {
      log.error('验证外边框失败:', error)
      return {
        isValid: false,
        actualBorder: '验证失败'
      }
    }
  }

  /**
   * 验证所有边框
   */
  private async validateAllBorders(
    worksheet: UniverWorksheet,
    rangeAddress: string,
    cells: string[],
    expectedBorderColor?: string
  ): Promise<{ isValid: boolean; actualBorder: string; actualBorderColor?: string }> {
    try {
      log.debug('验证所有边框:', { rangeAddress, cells, expectedBorderColor })

      // 检查每个单元格的所有边框
      const borderChecks = []

      for (const cellAddress of cells) {
        const topBorder = await this.checkCellBorder(worksheet, cellAddress, 'top', expectedBorderColor)
        const bottomBorder = await this.checkCellBorder(worksheet, cellAddress, 'bottom', expectedBorderColor)
        const leftBorder = await this.checkCellBorder(worksheet, cellAddress, 'left', expectedBorderColor)
        const rightBorder = await this.checkCellBorder(worksheet, cellAddress, 'right', expectedBorderColor)

        borderChecks.push({
          cell: cellAddress,
          top: topBorder,
          bottom: bottomBorder,
          left: leftBorder,
          right: rightBorder,
          allPresent: topBorder && bottomBorder && leftBorder && rightBorder
        })
      }

      const allCellsHaveAllBorders = borderChecks.every(check => check.allPresent)

      log.debug('所有边框检查结果:', { borderChecks, allCellsHaveAllBorders })

      return {
        isValid: allCellsHaveAllBorders,
        actualBorder: allCellsHaveAllBorders ? 'all' : '部分或无内边框'
      }
    } catch (error) {
      log.error('验证所有边框失败:', error)
      return {
        isValid: false,
        actualBorder: '验证失败'
      }
    }
  }

  /**
   * 验证粗边框
   */
  private async validateThickBorder(
    worksheet: UniverWorksheet,
    rangeAddress: string,
    cells: string[],
    expectedBorderColor?: string
  ): Promise<{ isValid: boolean; actualBorder: string; actualBorderColor?: string }> {
    try {
      log.debug('验证粗边框:', { rangeAddress, cells, expectedBorderColor })

      // 解析范围
      const parsed = this.parseRange(rangeAddress)
      const { startRow, endRow, startColumn, endColumn } = parsed

      // 检查边界单元格是否有粗边框
      const borderChecks = []

      // 检查顶部粗边框
      for (let col = startColumn; col <= endColumn; col++) {
        const cellAddress = this.getCellAddress(startRow, col)
        const hasBorder = await this.checkCellBorder(worksheet, cellAddress, 'top', expectedBorderColor, true)
        borderChecks.push({ cell: cellAddress, position: 'top', hasBorder })
      }

      // 检查底部粗边框
      for (let col = startColumn; col <= endColumn; col++) {
        const cellAddress = this.getCellAddress(endRow, col)
        const hasBorder = await this.checkCellBorder(worksheet, cellAddress, 'bottom', expectedBorderColor, true)
        borderChecks.push({ cell: cellAddress, position: 'bottom', hasBorder })
      }

      // 检查左粗边框
      for (let row = startRow; row <= endRow; row++) {
        const cellAddress = this.getCellAddress(row, startColumn)
        const hasBorder = await this.checkCellBorder(worksheet, cellAddress, 'left', expectedBorderColor, true)
        borderChecks.push({ cell: cellAddress, position: 'left', hasBorder })
      }

      // 检查右粗边框
      for (let row = startRow; row <= endRow; row++) {
        const cellAddress = this.getCellAddress(row, endColumn)
        const hasBorder = await this.checkCellBorder(worksheet, cellAddress, 'right', expectedBorderColor, true)
        borderChecks.push({ cell: cellAddress, position: 'right', hasBorder })
      }

      const allBordersPresent = borderChecks.every(check => check.hasBorder)

      log.debug('粗边框检查结果:', { borderChecks, allBordersPresent })

      return {
        isValid: allBordersPresent,
        actualBorder: allBordersPresent ? 'thick' : '部分或无粗边框'
      }
    } catch (error) {
      log.error('验证粗边框失败:', error)
      return {
        isValid: false,
        actualBorder: '验证失败'
      }
    }
  }

  /**
   * 检查单元格边框
   */
  private async checkCellBorder(
    worksheet: UniverWorksheet,
    cellAddress: string,
    position: 'top' | 'bottom' | 'left' | 'right',
    expectedColor?: string,
    isThick?: boolean
  ): Promise<boolean> {
    try {
      log.debug('检查单元格边框:', { cellAddress, position, expectedColor, isThick })

      const range = worksheet.getRange(cellAddress)
      if (!range) {
        log.debug('无法获取单元格范围:', cellAddress)
        return false
      }

      // 获取单元格数据和样式信息
      const cellData = range.getCellData()
      let style: UniverCellStyle = {}

      // 根据Univer文档，样式可能是ID引用或直接的样式对象
      if (cellData?.s) {
        if (typeof cellData.s === 'string') {
          // 如果是字符串，说明是样式ID，需要从工作簿样式表中获取
          const workbook = this.univerAPI.getActiveWorkbook()
          let styles = {}

          try {
            // 使用save()方法获取工作簿数据，按照文档建议
            try {
              const workbookData = await (workbook as unknown as { save(): Promise<{ styles?: Record<string, UniverCellStyle> }> }).save()
              styles = workbookData?.styles || {}
              log.debug('使用save()获取样式表:', { totalStyles: Object.keys(styles).length })
            } catch (saveError) {
              log.debug('save()方法失败，尝试其他方法:', saveError)

              // 备用方法：尝试从Univer实例获取
              const univerInstance = (this.univerAPI as unknown as { _univerInstance?: unknown })?._univerInstance
              if (univerInstance) {
                const currentWorkbook = (univerInstance as { getCurrentUniverSheetInstance(): unknown }).getCurrentUniverSheetInstance()
                const workbookSnapshot = (currentWorkbook as { save?(): unknown; getSnapshot?(): unknown })?.save?.() || (currentWorkbook as { save?(): unknown; getSnapshot?(): unknown })?.getSnapshot?.()
                styles = (workbookSnapshot as { styles?: Record<string, UniverCellStyle> })?.styles || {}
              }
            }

            log.debug('获取到的样式表:', { totalStyles: Object.keys(styles).length, styleId: cellData.s })
          } catch (error) {
            log.debug('获取样式表失败:', error)
          }

          style = styles[cellData.s] || {}
          log.debug('从样式表获取样式:', { styleId: cellData.s, style, hasStyle: !!styles[cellData.s] })
        } else {
          // 如果是对象，直接使用
          style = cellData.s
          log.debug('直接使用样式对象:', style)
        }
      }

      // 根据Univer文档，边框信息存储在bd属性中
      const borderData = style.bd
      if (!borderData) {
        log.debug('单元格无边框数据:', cellAddress)
        return false
      }

      // 根据位置获取对应的边框信息
      let borderInfo
      switch (position) {
        case 'top':
          borderInfo = borderData.t
          break
        case 'bottom':
          borderInfo = borderData.b
          break
        case 'left':
          borderInfo = borderData.l
          break
        case 'right':
          borderInfo = borderData.r
          break
      }

      if (!borderInfo) {
        log.debug(`单元格 ${cellAddress} 的 ${position} 边框不存在`)
        return false
      }

      log.debug('边框信息:', { cellAddress, position, borderInfo })

      // 检查边框样式 (s属性)
      const borderStyle = borderInfo.s
      if (borderStyle === undefined || borderStyle === null) {
        log.debug(`单元格 ${cellAddress} 的 ${position} 边框样式未设置`)
        return false
      }

      // 如果需要检查粗边框
      if (isThick) {
        // 根据Univer文档，边框样式值越大表示越粗
        // 通常粗边框的样式值会大于普通边框
        const isThickBorder = borderStyle >= 2 // 假设样式值2及以上为粗边框
        if (!isThickBorder) {
          log.debug(`单元格 ${cellAddress} 的 ${position} 边框不是粗边框，样式值: ${borderStyle}`)
          return false
        }
      } else {
        // 检查是否有边框（样式值大于0）
        if (borderStyle <= 0) {
          log.debug(`单元格 ${cellAddress} 的 ${position} 边框样式值无效: ${borderStyle}`)
          return false
        }
      }

      // 如果指定了期望的边框颜色，检查颜色
      if (expectedColor) {
        const borderColor = borderInfo.cl?.rgb
        if (!borderColor) {
          log.debug(`单元格 ${cellAddress} 的 ${position} 边框无颜色信息`)
          return false
        }

        // 颜色比较（标准化处理）
        const normalizeColor = (color: string) => {
          if (!color) return ''
          return color.toLowerCase().replace(/#/g, '')
        }

        const actualNormalized = normalizeColor(borderColor)
        const expectedNormalized = normalizeColor(expectedColor)

        if (actualNormalized !== expectedNormalized) {
          log.debug(`单元格 ${cellAddress} 的 ${position} 边框颜色不匹配:`, {
            expected: expectedColor,
            actual: borderColor,
            expectedNormalized,
            actualNormalized
          })
          return false
        }
      }

      log.debug(`单元格 ${cellAddress} 的 ${position} 边框验证通过`)
      return true

    } catch (error) {
      log.error(`检查单元格 ${cellAddress} 的 ${position} 边框失败:`, error)
      return false
    }
  }

  /**
   * 验证数据验证规则
   */
  private async validateDataValidation(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      // 获取指定单元格的范围
      const range = worksheet.getRange(rule.cell)
      if (!range) {
        return {
          success: false,
          message: `无法获取单元格 ${rule.cell}`
        }
      }

      log.debug('开始严格验证数据验证规则:', rule)

      // 使用Univer Facade API进行严格验证
      let dataValidationRule = null
      let hasValidDataValidation = false

      try {
        // 方法1：使用Facade API获取单元格的数据验证规则
        const fWorksheet = worksheet as {
          getRange?(cell: string): { getDataValidation?(): unknown };
          getDataValidations?(): unknown[]
        }
        const fRange = fWorksheet.getRange ? fWorksheet.getRange(rule.cell) : (range as { getDataValidation?(): unknown })

        if (fRange && fRange.getDataValidation) {
          dataValidationRule = fRange.getDataValidation()
          log.debug('获取到的数据验证规则:', dataValidationRule)
        }

        // 方法2：如果方法1失败，尝试获取工作表的所有数据验证规则
        if (!dataValidationRule && fWorksheet.getDataValidations) {
          const allValidations = fWorksheet.getDataValidations()
          log.debug('工作表所有数据验证规则:', allValidations)

          if (allValidations && allValidations.length > 0) {
            for (const validation of allValidations) {
              const ranges = (validation as { getRanges?(): unknown[] }).getRanges ? (validation as { getRanges(): unknown[] }).getRanges() : []
              const matchesCell = ranges.some((rangeItem: unknown) => {
                const range = rangeItem as { getA1Notation?(): string | undefined }
                if (!range.getA1Notation) return false
                const notationResult = range.getA1Notation()
                const notation = String(notationResult || '')
                return notation === rule.cell || (notation.length > 0 && rule.cell && notation.includes(rule.cell))
              })

              if (matchesCell) {
                dataValidationRule = validation
                break
              }
            }
          }
        }

        // 如果获取到数据验证规则，进行详细验证
        if (dataValidationRule) {
          log.debug('检测到数据验证规则，开始详细验证...')

          // 获取验证规则的类型和值
          const validationRule = dataValidationRule as { getCriteriaType?(): unknown; getCriteriaValues?(): unknown[] }
          const criteriaType = validationRule.getCriteriaType ? validationRule.getCriteriaType() : null
          const criteriaValues = validationRule.getCriteriaValues ? validationRule.getCriteriaValues() : []

          log.debug('验证规则类型:', criteriaType)
          log.debug('验证规则值:', criteriaValues)

          // 验证是否为列表类型（下拉菜单）
          if (rule.validationType === 'list') {
            // 检查是否为列表类型的数据验证
            const isListType = criteriaType === 'list' ||
                             criteriaType === 'LIST' ||
                             criteriaType === 'dropdown' ||
                             (criteriaValues && criteriaValues.length > 0)

            if (!isListType) {
              return {
                success: false,
                message: `单元格 ${rule.cell} 的数据验证类型不正确。\n期望类型：下拉菜单（列表）\n实际类型：${criteriaType}\n\n请重新设置为下拉菜单类型的数据验证。`,
                details: {
                  cell: rule.cell,
                  expectedType: 'list',
                  actualType: criteriaType,
                  hasDataValidation: true
                }
              }
            }

            // 验证数据源是否正确
            if (rule.source && criteriaValues && criteriaValues.length > 0) {
              const [operator, formula1, formula2] = criteriaValues
              log.debug('验证数据源:', { operator, formula1, formula2, expectedSource: rule.source })

              // 检查数据源是否匹配
              const sourceMatches = this.validateDataValidationSource(rule.source, String(formula1 || ''))

              if (!sourceMatches.isValid) {
                return {
                  success: false,
                  message: `单元格 ${rule.cell} 的数据验证选项不正确。\n${sourceMatches.message}\n\n请检查并重新设置正确的选项。`,
                  details: {
                    cell: rule.cell,
                    expectedSource: rule.source,
                    actualSource: formula1,
                    hasDataValidation: true,
                    sourceValidation: sourceMatches
                  }
                }
              }
            }

            hasValidDataValidation = true
          }
        }

      } catch (error) {
        log.debug('检查数据验证API失败:', error)
      }

      // 严格验证：如果没有检测到有效的数据验证，返回失败
      if (!hasValidDataValidation) {
        return {
          success: false,
          message: `单元格 ${rule.cell} 未设置正确的数据验证规则。\n\n请按照以下步骤设置：\n1. 选中单元格 ${rule.cell}\n2. 点击"数据"菜单\n3. 在"数据工具"组中点击"数据验证"\n4. 点击"新建规则"\n5. 条件类型选择"下拉菜单"\n6. ${rule.source?.includes('$') ? '选择"引用数据"，输入引用区间：' + rule.source : '选择"自定义"，添加选项：' + rule.source}\n7. 点击"确定"\n\n完成后单元格应显示下拉箭头。`,
          details: {
            cell: rule.cell,
            expectedValidationType: rule.validationType,
            expectedSource: rule.source,
            hasDataValidation: !!dataValidationRule,
            hasValidDataValidation: false
          }
        }
      }

      return {
        success: true,
        message: '数据验证规则设置正确！已检测到正确的数据验证配置。',
        details: {
          cell: rule.cell,
          validationType: rule.validationType,
          source: rule.source,
          hasDataValidation: true,
          hasValidDataValidation: true,
          dataValidationRule: dataValidationRule
        }
      }

    } catch (error) {
      log.error('验证数据验证规则时发生错误:', error)
      return {
        success: false,
        message: `验证数据验证规则时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证数据验证的数据源是否正确
   */
  private validateDataValidationSource(expectedSource: string, actualSource: string): { isValid: boolean; message: string } {
    if (!actualSource) {
      return {
        isValid: false,
        message: '未检测到数据验证的数据源配置'
      }
    }

    log.debug('验证数据源匹配:', { expectedSource, actualSource })

    // 处理引用数据的情况（如 $D$1:$D$4）
    if (expectedSource.includes('$')) {
      // 标准化引用格式
      const normalizedExpected = expectedSource.replace(/\$/g, '').toUpperCase()
      const normalizedActual = actualSource.replace(/\$/g, '').toUpperCase()

      if (normalizedActual.includes(normalizedExpected) || normalizedExpected.includes(normalizedActual)) {
        return {
          isValid: true,
          message: '引用数据源匹配'
        }
      }

      return {
        isValid: false,
        message: `引用数据源不匹配。\n期望引用：${expectedSource}\n实际引用：${actualSource}`
      }
    }

    // 处理自定义选项的情况（如 "优秀,良好,一般,较差"）
    else {
      const expectedOptions = expectedSource.split(',').map(opt => opt.trim())
      log.debug('期望的选项:', expectedOptions)

      // 检查实际数据源是否包含所有期望的选项
      const missingOptions = expectedOptions.filter(option => !actualSource.includes(option))

      if (missingOptions.length === 0) {
        return {
          isValid: true,
          message: '自定义选项完全匹配'
        }
      }

      // 检查是否至少包含部分选项
      const foundOptions = expectedOptions.filter(option => actualSource.includes(option))

      if (foundOptions.length > 0) {
        return {
          isValid: false,
          message: `自定义选项不完整。\n期望选项：${expectedOptions.join('、')}\n缺少选项：${missingOptions.join('、')}\n已找到选项：${foundOptions.join('、')}`
        }
      }

      return {
        isValid: false,
        message: `自定义选项完全不匹配。\n期望选项：${expectedOptions.join('、')}\n实际数据源：${actualSource}`
      }
    }
  }

  /**
   * 验证单元格合并
   */
  private async validateCellMerge(rule: ValidationRule): Promise<ValidationResult> {
    log.validation('开始验证单元格合并:', rule)

    if (!rule.mergedRanges || rule.mergedRanges.length === 0) {
      return {
        success: false,
        message: '验证规则中未指定需要合并的范围'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      const validationResults = []

      // 验证每个指定的合并范围
      for (const mergedRange of rule.mergedRanges) {
        const { range, description } = mergedRange

        try {
          const rangeObj = worksheet.getRange(range)
          if (!rangeObj) {
            validationResults.push({
              range,
              description,
              isMerged: false,
              error: '无法获取范围对象'
            })
            continue
          }

          // 使用Univer FRange.isMerged() API检查是否合并
          const isMerged = rangeObj.isMerged()
          log.validation(`范围 ${range} 合并状态:`, isMerged)

          validationResults.push({
            range,
            description,
            isMerged,
            expected: true
          })

        } catch (error) {
          log.error(`检查范围 ${range} 合并状态失败:`, error)
          validationResults.push({
            range,
            description,
            isMerged: false,
            error: `检查失败: ${error}`
          })
        }
      }

      // 检查所有范围是否都已合并
      const allMerged = validationResults.every(result => result.isMerged === true)
      const failedRanges = validationResults.filter(result => !result.isMerged)

      if (allMerged) {
        return {
          success: true,
          message: '所有指定范围都已正确合并！',
          details: { validationResults }
        }
      } else {
        const failedMessages = failedRanges.map(result =>
          `${result.description} (${result.range})${result.error ? ': ' + result.error : ''}`
        ).join('\n')

        return {
          success: false,
          message: `以下范围未正确合并：\n${failedMessages}\n\n请按照操作步骤进行单元格合并操作。`,
          details: { validationResults }
        }
      }

    } catch (error) {
      log.error('验证单元格合并失败:', error)
      return {
        success: false,
        message: `验证过程中发生错误: ${error}`
      }
    }
  }

  /**
   * 验证文本换行
   */
  private async validateTextWrap(rule: ValidationRule): Promise<ValidationResult> {
    log.validation('开始验证文本换行:', rule)

    if (!rule.cells || rule.cells.length === 0) {
      return {
        success: false,
        message: '验证规则中未指定需要验证的单元格'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      const validationResults = []

      // 验证每个指定的单元格
      for (const cellConfig of rule.cells) {
        const { cell, wrapType, expectedText, description } = cellConfig

        if (!cell) {
          validationResults.push({
            cell: '未知',
            description: description || '配置错误',
            success: false,
            error: '单元格地址未指定'
          })
          continue
        }

        try {
          const rangeObj = worksheet.getRange(cell)
          if (!rangeObj) {
            validationResults.push({
              cell,
              description: description || `验证${cell}`,
              success: false,
              error: '无法获取单元格范围'
            })
            continue
          }

          let isValid = true
          let errorMessage = ''
          let actualText: string | null = null

          // 验证自动换行设置
          if (wrapType === 'auto') {
            // 使用Univer FRange.getWrap() API检查是否设置了自动换行
            const hasWrap = rangeObj.getWrap()
            log.validation(`单元格 ${cell} 自动换行状态:`, hasWrap)

            if (!hasWrap) {
              isValid = false
              errorMessage = '未设置自动换行'
            }
          }

          // 验证强制换行（手动换行）
          if (wrapType === 'manual' && expectedText) {
            // 尝试多种方法获取单元格的实际文本内容

            try {
              // 方法1：使用getValue()
              const rawValue = rangeObj.getValue()
              log.validation(`单元格 ${cell} getValue() 结果:`, rawValue)

              if (rawValue !== null && rawValue !== undefined) {
                actualText = String(rawValue)
              }

              // 方法2：如果getValue()没有结果，尝试getDisplayValue()
              if (!actualText && rangeObj.getDisplayValue) {
                const displayValue = rangeObj.getDisplayValue()
                log.validation(`单元格 ${cell} getDisplayValue() 结果:`, displayValue)
                if (displayValue !== null && displayValue !== undefined) {
                  actualText = String(displayValue)
                }
              }

              // 方法3：如果还是没有结果，尝试从cellData获取
              if (!actualText) {
                const cellData = rangeObj.getCellData()
                log.validation(`单元格 ${cell} getCellData() 结果:`, cellData)

                if (cellData && cellData.v !== null && cellData.v !== undefined) {
                  actualText = String(cellData.v)
                } else if (cellData && cellData.p && cellData.p.body && cellData.p.body.dataStream) {
                  // 尝试从富文本数据中获取
                  actualText = cellData.p.body.dataStream
                }
              }

              // 方法4：尝试使用getRawValue()（如果存在）
              if (!actualText && 'getRawValue' in rangeObj && typeof (rangeObj as { getRawValue?: () => unknown }).getRawValue === 'function') {
                const rawValue2 = (rangeObj as { getRawValue: () => unknown }).getRawValue()
                log.validation(`单元格 ${cell} getRawValue() 结果:`, rawValue2)
                if (rawValue2 !== null && rawValue2 !== undefined) {
                  actualText = String(rawValue2)
                }
              }

            } catch (error) {
              log.error(`获取单元格 ${cell} 内容失败:`, error)
            }

            log.validation(`单元格 ${cell} 最终实际文本:`, actualText)
            log.validation(`单元格 ${cell} 期望文本:`, expectedText)

            // 检查文本内容是否包含换行符并匹配期望内容
            if (!actualText) {
              isValid = false
              errorMessage = '无法获取单元格文本内容，请确保已输入文本'
            } else {
              // 更灵活的验证：检查是否包含期望的文本部分和换行符
              const expectedParts = expectedText.split('\n')
              const hasAllParts = actualText ? expectedParts.every(part => actualText!.includes(part)) : false
              const hasLineBreak = actualText ? (actualText.includes('\n') || actualText.includes('\r\n') || actualText.includes('\r')) : false

              log.validation(`期望的文本部分:`, expectedParts)
              log.validation(`是否包含所有部分:`, hasAllParts)
              log.validation(`是否包含换行符:`, hasLineBreak)

              if (!hasAllParts) {
                isValid = false
                errorMessage = `文本内容不完整。期望包含: ${expectedParts.join(' 和 ')}，实际: "${actualText}"`
              } else if (!hasLineBreak) {
                isValid = false
                errorMessage = `文本缺少换行符。请在"Excel"和"学习"之间插入换行符（Alt+Enter）`
              } else if (actualText !== expectedText) {
                // 如果包含所有部分和换行符，但格式不完全匹配，给出提示但可能仍然通过
                log.validation(`文本格式略有不同，但包含必要元素`)
                // 可以选择通过验证，或者要求严格匹配
                isValid = true // 暂时设为通过，因为包含了必要的元素
              }
            }
          }

          validationResults.push({
            cell,
            description: description || `验证${cell}`,
            wrapType,
            expectedText,
            actualText: wrapType === 'manual' ? actualText : undefined,
            hasWrap: wrapType === 'auto' ? rangeObj.getWrap() : undefined,
            success: isValid,
            error: errorMessage || undefined
          })

        } catch (error) {
          log.error(`检查单元格 ${cell} 换行状态失败:`, error)
          validationResults.push({
            cell,
            description: description || `验证${cell}`,
            success: false,
            error: `检查失败: ${error}`
          })
        }
      }

      // 检查所有单元格是否都验证通过
      const allValid = validationResults.every(result => result.success)
      const failedCells = validationResults.filter(result => !result.success)

      if (allValid) {
        return {
          success: true,
          message: '所有文本换行设置都正确！',
          details: { validationResults }
        }
      } else {
        const failedMessages = failedCells.map(result =>
          `${result.description} (${result.cell})${result.error ? ': ' + result.error : ''}`
        ).join('\n')

        return {
          success: false,
          message: `以下单元格的文本换行设置不正确：\n${failedMessages}\n\n请按照操作步骤进行文本换行设置。`,
          details: { validationResults }
        }
      }

    } catch (error) {
      log.error('验证文本换行失败:', error)
      return {
        success: false,
        message: `验证过程中发生错误: ${error}`
      }
    }
  }

  /**
   * 验证公式填充
   */
  private async validateFormulaFill(rule: ValidationRule): Promise<ValidationResult> {
    log.validation('开始验证公式填充:', rule)

    if (!rule.range || !rule.expectedFormulas || rule.expectedFormulas.length === 0) {
      return {
        success: false,
        message: '验证规则中未指定范围或期望的公式'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      interface FormulaValidationResult {
        cell: string
        success: boolean
        expectedFormula: string
        actualFormula: string | null
        expectedValue?: number | null
        actualValue?: unknown
        formulaMatch?: boolean
        calculationCorrect?: boolean
        error?: string
      }

      const validationResults: FormulaValidationResult[] = []
      let allFormulasCorrect = true

      // 验证每个单元格的公式
      for (const expectedFormula of rule.expectedFormulas) {
        const { cell, formula: expectedFormulaText } = expectedFormula

        try {
          const range = worksheet.getRange(cell)
          if (!range) {
            validationResults.push({
              cell,
              success: false,
              error: '无法获取单元格范围',
              expectedFormula: expectedFormulaText,
              actualFormula: null
            })
            allFormulasCorrect = false
            continue
          }

          // 获取单元格公式 - 优先使用getFormula方法
          let actualFormula = ''

          try {
            // 方法1：使用range.getFormula()方法（推荐）
            if (range.getFormula) {
              actualFormula = range.getFormula() || ''
              log.debug(`${cell} getFormula():`, actualFormula)
            }

            // 方法2：如果方法1失败，尝试getCellData().f
            if (!actualFormula) {
              const cellData = range.getCellData()
              actualFormula = cellData?.f || ''
              log.debug(`${cell} getCellData().f:`, actualFormula)
            }

            log.debug(`${cell} 最终获取的公式:`, actualFormula)

          } catch (formulaError) {
            log.error(`获取 ${cell} 公式时出错:`, formulaError)
            actualFormula = ''
          }

          // 标准化公式格式（去除空格，统一大小写）
          const normalizeFormula = (formula: string) => {
            return formula.replace(/\s+/g, '').toUpperCase()
          }

          const expectedNormalized = normalizeFormula(expectedFormulaText)
          const actualNormalized = normalizeFormula(actualFormula)

          const isFormulaMatch = actualNormalized === expectedNormalized

          // 验证计算结果
          let calculationCorrect = true
          let actualValue = null
          let expectedValue = null

          if (isFormulaMatch) {
            // 如果公式正确，验证计算结果
            actualValue = range.getValue()
            const extractedValue = this.extractCellValue(actualValue)

            // 计算期望值（基于公式）
            expectedValue = this.calculateExpectedValue(cell, expectedFormulaText, worksheet)
            calculationCorrect = this.compareValues(extractedValue, expectedValue)
          }

          const cellResult: FormulaValidationResult = {
            cell,
            success: isFormulaMatch && calculationCorrect,
            expectedFormula: expectedFormulaText,
            actualFormula,
            expectedValue,
            actualValue: actualValue ? this.extractCellValue(actualValue) : null,
            formulaMatch: isFormulaMatch,
            calculationCorrect
          }

          validationResults.push(cellResult)

          if (!cellResult.success) {
            allFormulasCorrect = false
          }

          log.validation(`单元格 ${cell} 验证结果:`, cellResult)

        } catch (error) {
          log.error(`验证单元格 ${cell} 时出错:`, error)
          validationResults.push({
            cell,
            success: false,
            error: String(error),
            expectedFormula: expectedFormulaText,
            actualFormula: null
          })
          allFormulasCorrect = false
        }
      }

      // 生成验证结果消息
      if (allFormulasCorrect) {
        return {
          success: true,
          message: '所有公式都已正确填充！快速填充功能使用成功。',
          details: { validationResults }
        }
      } else {
        const failedCells = validationResults.filter(result => !result.success)
        const cellsWithoutFormula = failedCells.filter(result => !result.actualFormula || result.actualFormula.trim() === '')
        const cellsWithWrongFormula = failedCells.filter(result => result.actualFormula && result.actualFormula.trim() !== '' && !result.formulaMatch)
        const cellsWithWrongCalculation = failedCells.filter(result => result.formulaMatch && !result.calculationCorrect)

        let message = '公式填充不完整或不正确：\n\n'

        if (cellsWithoutFormula.length > 0) {
          const missingCells = cellsWithoutFormula.map(r => r.cell).join(', ')
          message += `❌ 缺少公式的单元格：${missingCells}\n`
          message += `   这些单元格需要包含对应的乘法公式\n\n`
        }

        if (cellsWithWrongFormula.length > 0) {
          message += `❌ 公式错误的单元格：\n`
          cellsWithWrongFormula.forEach(result => {
            message += `   ${result.cell}: 期望 ${result.expectedFormula}，实际 ${result.actualFormula}\n`
          })
          message += '\n'
        }

        if (cellsWithWrongCalculation.length > 0) {
          message += `❌ 计算结果错误的单元格：\n`
          cellsWithWrongCalculation.forEach(result => {
            message += `   ${result.cell}: 期望结果 ${result.expectedValue}，实际结果 ${result.actualValue}\n`
          })
          message += '\n'
        }

        // 根据具体情况给出操作建议
        if (cellsWithoutFormula.length > 0) {
          const hasC2Formula = validationResults.find(r => r.cell === 'C2')?.success
          if (hasC2Formula) {
            message += `💡 操作建议：\n`
            message += `1. 选中C2单元格（已有正确公式）\n`
            message += `2. 将鼠标移到C2单元格的右下角，直到出现十字符号（填充柄）\n`
            message += `3. 方法一：按住鼠标左键，向下拖拽到C11单元格\n`
            message += `4. 方法二：或者在出现十字符号时，双击鼠标左键实现快速填充\n`
            message += `5. 确保C3到C11都自动填入了对应的公式`
          } else {
            message += `💡 操作建议：\n`
            message += `1. 先在C2中输入公式 =A2*B2\n`
            message += `2. 按回车键确认公式\n`
            message += `3. 再次选中C2单元格\n`
            message += `4. 使用快速填充功能将公式复制到C3-C11`
          }
        } else {
          message += `💡 请检查公式是否正确，确保每个单元格的公式都是对应行的数量乘以单价`
        }

        return {
          success: false,
          message,
          details: { validationResults }
        }
      }

    } catch (error) {
      log.error('验证公式填充失败:', error)
      return {
        success: false,
        message: `验证过程中发生错误: ${error}`
      }
    }
  }

  /**
   * 计算期望值（基于公式和工作表数据）
   */
  private calculateExpectedValue(cell: string, formula: string, worksheet: UniverWorksheet): number | null {
    try {
      // 解析公式中的单元格引用（简单的乘法公式 =A2*B2）
      const match = formula.match(/^=([A-Z]+)(\d+)\*([A-Z]+)(\d+)$/i)
      if (!match) {
        log.debug(`无法解析公式: ${formula}`)
        return null
      }

      const [, col1, row1, col2, row2] = match
      const cell1 = `${col1}${row1}`
      const cell2 = `${col2}${row2}`

      // 获取两个单元格的值
      const range1 = worksheet.getRange(cell1)
      const range2 = worksheet.getRange(cell2)

      if (!range1 || !range2) {
        log.debug(`无法获取单元格范围: ${cell1} 或 ${cell2}`)
        return null
      }

      const value1 = this.extractCellValue(range1.getValue())
      const value2 = this.extractCellValue(range2.getValue())

      // 确保两个值都是数字
      const num1 = typeof value1 === 'number' ? value1 : parseFloat(String(value1))
      const num2 = typeof value2 === 'number' ? value2 : parseFloat(String(value2))

      if (isNaN(num1) || isNaN(num2)) {
        log.debug(`单元格值不是数字: ${cell1}=${value1}, ${cell2}=${value2}`)
        return null
      }

      const result = num1 * num2
      log.debug(`计算期望值: ${cell1}(${num1}) * ${cell2}(${num2}) = ${result}`)
      return result

    } catch (error) {
      log.error('计算期望值时出错:', error)
      return null
    }
  }
}

/**
 * 创建验证服务实例
 */
export function createValidationService(univerAPI: UniverAPI): ExcelValidationService {
  return new ExcelValidationService(univerAPI)
}

/**
 * 验证任务的便捷函数
 */
export async function validateTask(univerAPI: UniverAPI, validationRule: ValidationRule): Promise<ValidationResult> {
  const service = createValidationService(univerAPI)
  return await service.validateTask(validationRule)
}

