import { test, expect } from '@playwright/test';

test.describe('登录功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到登录页面
    await page.goto('http://localhost:5173/auth/signin');
  });

  test('应该能够成功登录', async ({ page }) => {
    // 填写登录表单
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', '123456');
    
    // 点击登录按钮
    await page.click('button[type="submit"]');
    
    // 等待重定向到仪表板
    await page.waitForURL('**/dashboard');
    
    // 验证登录成功
    await expect(page).toHaveURL(/.*dashboard/);
    
    // 验证页面包含用户信息
    await expect(page.locator('text=仪表板')).toBeVisible();
  });

  test('应该显示错误信息当凭据无效时', async ({ page }) => {
    // 填写错误的登录信息
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'wrongpassword');
    
    // 点击登录按钮
    await page.click('button[type="submit"]');
    
    // 验证错误信息显示
    await expect(page.locator('text=邮箱或密码错误')).toBeVisible();
  });

  test('应该验证必填字段', async ({ page }) => {
    // 尝试提交空表单
    await page.click('button[type="submit"]');
    
    // 验证HTML5验证
    const emailInput = page.locator('input[name="email"]');
    const passwordInput = page.locator('input[name="password"]');
    
    await expect(emailInput).toHaveAttribute('required');
    await expect(passwordInput).toHaveAttribute('required');
  });

  test('登录页面应该有正确的UI元素', async ({ page }) => {
    // 验证页面标题
    await expect(page).toHaveTitle(/登录/);
    
    // 验证表单元素存在
    await expect(page.locator('input[name="email"]')).toBeVisible();
    await expect(page.locator('input[name="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
    
    // 验证链接存在
    await expect(page.locator('a[href="/auth/signup"]')).toBeVisible();
    await expect(page.locator('a[href="/auth/forgot-password"]')).toBeVisible();
  });
});
