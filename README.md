# Excel学习平台 - Svelte版

这是一个基于 SvelteKit 5 和 UniverJS 的 Excel 在线学习平台，从 Next.js 版本迁移而来。

## 功能特性

- 🎯 **实时练习**: 在真实的 Excel 环境中练习，即时获得反馈
- 📚 **系统化学习**: 从基础到进阶，循序渐进的学习路径
- 🏆 **成就系统**: 完成任务获得积分，解锁更多高级功能
- 💻 **在线实践**: 无需安装软件，浏览器即可开始学习
- 📈 **进度跟踪**: 清晰的学习进度，掌握每一个知识点
- ⚡ **即时反馈**: 实时验证操作结果，快速纠正错误

## 技术栈

- **前端框架**: SvelteKit 5
- **Excel组件**: UniverJS
- **数据库**: PostgreSQL + Prisma ORM
- **认证**: Auth.js (SvelteKit)
- **样式**: Tailwind CSS
- **语言**: TypeScript

## 开发环境设置

### 1. 克隆项目

```bash
git clone https://github.com/tscodeplus/Learn-Excel-Svelte.git
cd Learn-Excel-Svelte
```

### 2. 安装依赖

```bash
pnpm install
```

### 3. 环境变量配置

复制 `.env.example` 到 `.env` 并填写相应的配置：

```bash
cp .env.example .env
```

需要配置的环境变量：
- `DATABASE_URL`: PostgreSQL 数据库连接字符串
- `AUTH_SECRET`: 认证密钥
- `SMTP_*`: 邮件服务配置（用于邮箱验证）

### 4. 数据库设置

```bash
# 生成 Prisma 客户端
pnpm prisma generate

# 运行数据库迁移
pnpm prisma migrate dev

# 填充种子数据
pnpm prisma db seed
```

### 5. 启动开发服务器

```bash
pnpm dev
```

访问 [http://localhost:5173](http://localhost:5173) 查看应用。

## 构建和部署

### 构建生产版本

```bash
pnpm build
```

### 预览生产版本

```bash
pnpm preview
```

## 学习路径

### 1. 初识Excel (7个任务)
- 基础操作和数据输入
- 格式设置和单元格操作
- 积分：10-20分

### 2. 基本公式 (4个任务)
- 常用函数学习
- 公式应用实践
- 积分：20-30分

### 3. 常用操作 (7个任务)
- 数据排序和筛选
- 条件格式设置
- 积分：25-40分

### 4. 进阶公式 (2个任务)
- 高级函数应用
- 复杂公式构建
- 积分：50分

### 5. 进阶操作 (3个任务)
- 数据透视表
- 图表制作
- 积分：50-60分

## 项目结构

```
src/
├── lib/                    # 核心库文件
│   ├── components/         # Svelte 组件
│   │   ├── UniverSheet.svelte
│   │   ├── UniverWrapper.svelte
│   │   └── lazy.ts
│   ├── types/             # TypeScript 类型定义
│   ├── auth.ts            # 认证配置
│   ├── db.ts              # 数据库连接
│   ├── email.ts           # 邮件服务
│   ├── invite-codes.ts    # 邀请码系统
│   ├── logger.ts          # 日志系统
│   └── validation.ts      # 任务验证系统
├── routes/                # 页面路由
│   ├── api/               # API 路由
│   ├── auth/              # 认证页面
│   ├── dashboard/         # 仪表板
│   ├── level/             # 关卡页面
│   ├── task/              # 任务页面
│   └── +page.svelte       # 主页
├── app.css                # 全局样式
├── app.d.ts               # 类型声明
├── app.html               # HTML 模板
└── hooks.server.ts        # 服务器钩子
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：

- GitHub Issues: [https://github.com/tscodeplus/Learn-Excel-Svelte/issues](https://github.com/tscodeplus/Learn-Excel-Svelte/issues)
- Email: <EMAIL>
