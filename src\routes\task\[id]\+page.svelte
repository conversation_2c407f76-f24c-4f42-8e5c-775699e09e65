<script lang="ts">
  import { page } from '$app/stores'
  import { signOut } from '@auth/sveltekit/client'
  import UniverWrapper from '$lib/components/UniverWrapper.svelte'
  import { validateTask } from '$lib/validation'
  import type { UniverInstance, UniverAPI } from '$lib/types/univer'
  import { navbarStore } from '$lib/stores/navbar'
  import { onMount, afterUpdate } from 'svelte'
  
  export let data
  
  const { session, task, level } = data
  
  let univerInstance: UniverInstance | null = null
  let univerAPI: UniverAPI | null = null
  let validating = false
  let validationResult: { success: boolean; message: string } | null = null
  
  // 解析任务配置
  $: validationRule = task.validation ? JSON.parse(task.validation) : null
  $: initialData = task.initialData ? JSON.parse(task.initialData) : null

  // 设置导航栏数据，显示子关卡链接
  onMount(() => {
    if (level) {
      // 子关卡链接应该指向主任务（父级别），而不是当前子级别
      const parentLevelId = level.parent?.id || level.parentId || level.id
      navbarStore.setNavbarData({
        showLevelList: true,
        levelListHref: `/level/${parentLevelId}`
      })
    }
    // 确保页面加载时滚动到顶部
    window.scrollTo(0, 0)
  })
  
  const handleUniverReady = (instance: UniverInstance, api: UniverAPI) => {
    univerInstance = instance
    univerAPI = api
    console.log('Univer 已准备就绪')
  }
  
  const handleValidation = async () => {
    if (!univerAPI || !validationRule) {
      validationResult = {
        success: false,
        message: 'Excel组件未加载完成，请稍后重试'
      }
      return
    }

    validating = true
    validationResult = null

    try {
      const result = await validateTask(univerAPI, validationRule)

      if (result.success) {
        // 提交任务完成
        await submitTaskCompletion()

        // 显示成功消息，与原版完全一致
        validationResult = {
          success: true,
          message: '闯关完成！3秒后返回关卡列表...'
        }

        // 3秒后跳转，与原版逻辑一致
        setTimeout(() => {
          if (level.parentId) {
            // 子关卡完成后跳转到父级主任务页面
            window.location.href = `/level/${level.parentId}`
          } else {
            // 主任务完成后跳转到dashboard
            window.location.href = '/dashboard'
          }
        }, 3000)
      } else {
        // 显示失败消息，与原版完全一致
        validationResult = {
          success: false,
          message: '闯关失败，\n请检查你的操作是否正确'
        }
      }
    } catch (error) {
      console.error('验证失败:', error)
      validationResult = {
        success: false,
        message: '验证过程中发生错误，请检查任务配置'
      }
    } finally {
      validating = false
    }
  }
  
  const submitTaskCompletion = async () => {
    try {
      const response = await fetch('/api/progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          levelId: task.levelId,
          completed: true,
          score: level.points
        })
      })
      
      if (response.ok) {
        // 可以添加成功提示或重定向
        console.log('任务完成已提交')
      }
    } catch (error) {
      console.error('提交任务完成失败:', error)
    }
  }
  
  const handleSignOut = () => {
    signOut({ callbackUrl: '/' })
  }
</script>

<svelte:head>
  <title>{task.name} - Excel学习平台</title>
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50" style="padding-top: 6rem;">
  <div class="max-w-full mx-auto pb-4 px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-12rem)]">
      <!-- 任务说明面板 -->
      <div class="lg:col-span-1 relative">
        <div class="bg-white shadow-xl rounded-2xl p-6 border border-gray-100 h-full flex flex-col">
          <!-- 任务头部 - 更紧凑 -->
          <div class="text-center mb-6">
            <h1 class="text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">
              {task.name}
            </h1>
            <div class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
              {#if task.type === 'formula'}
                公式练习
              {:else if task.type === 'format'}
                格式设置
              {:else if task.type === 'chart'}
                图表制作
              {:else if task.type === 'pivot'}
                数据透视表
              {:else}
                基础操作
              {/if}
            </div>
          </div>

          <!-- 任务详情标题 -->
          <div class="flex items-center mb-4">
            <div class="flex items-center space-x-2">
              <div class="w-5 h-5 bg-blue-500 rounded-lg flex items-center justify-center">
                <span class="text-white text-xs">📋</span>
              </div>
              <h2 class="text-base font-bold text-gray-900">
                任务详情
              </h2>
            </div>
          </div>

          <!-- 任务描述 -->
          <div class="mb-2 flex-1 overflow-y-auto scrollbar-univer pr-3 max-h-none">
            <div class="bg-gray-50 rounded-xl p-4 border border-gray-200">
              {#each task.description.split('\n') as line, index}
                {#if line.trim().match(/^\d+\./)}
                  <div class="flex items-start mb-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <span class="flex w-6 h-6 bg-gradient-to-r from-blue-500 to-indigo-500 text-white text-xs rounded-full items-center justify-center mr-2 mt-0.5 flex-shrink-0 font-bold shadow-sm">
                      {line.trim().match(/^(\d+)\./)?.[1]}
                    </span>
                    <span class="text-gray-800 text-sm leading-relaxed">{line.replace(/^\d+\.\s*/, '')}</span>
                  </div>
                {:else if line.includes('操作步骤：') || line.includes('任务说明：') || line.includes('完成后')}
                  <div class="font-bold text-gray-900 mt-4 mb-3 text-base flex items-center">
                    <span class="mr-2 text-sm">📝</span>
                    {line}
                  </div>
                {:else if line.includes('提示：')}
                  <div class="mt-3 p-3 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg">
                    <div class="flex items-start">
                      <span class="text-yellow-600 text-sm mr-2">💡</span>
                      <span class="text-gray-800 text-sm leading-relaxed">
                        <span class="font-semibold text-yellow-800">提示：</span>
                        {line.replace('提示：', '')}
                      </span>
                    </div>
                  </div>
                {:else if line.trim()}
                  <div class="mb-1 text-gray-700 text-sm leading-relaxed">
                    {line}
                  </div>
                {/if}
              {/each}
            </div>
          </div>

          <!-- 底部区域 -->
          <div class="mt-auto">
            <!-- 统计信息 -->
            <div class="grid grid-cols-2 gap-3 mb-6">
              <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-3 border border-green-200 text-center">
                <div class="text-xl font-bold text-green-700 mb-1">{level.points}</div>
                <div class="text-xs text-green-600 flex items-center justify-center">
                  <span class="mr-1 text-xs">💎</span>
                  经验值
                </div>
              </div>

              <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-3 border border-yellow-200 text-center">
                <div class="flex items-center justify-center space-x-1 mb-1">
                  {#each Array(5) as _, i}
                    <span class="text-sm {i < level.difficulty ? 'text-yellow-500' : 'text-gray-300'}">
                      ★
                    </span>
                  {/each}
                </div>
                <div class="text-xs text-yellow-600 flex items-center justify-center">
                  <span class="mr-1 text-xs">⚡</span>
                  难度等级
                </div>
              </div>
            </div>

            <!-- 任务完成/未完成提示 -->
            {#if validationResult}
              <div class="rounded-xl p-4 mb-6 border {validationResult.success
                ? 'bg-gradient-to-r from-green-50 to-emerald-50 text-green-800 border-green-200'
                : 'bg-gradient-to-r from-red-50 to-pink-50 text-red-800 border-red-200'}">
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 rounded-full flex items-center justify-center {validationResult.success ? 'bg-green-500' : 'bg-red-500'}">
                    <span class="text-white text-sm">
                      {validationResult.success ? '✅' : '❌'}
                    </span>
                  </div>
                  <div class="font-medium whitespace-pre-line">{validationResult.message}</div>
                </div>
              </div>
            {/if}

            <!-- 提交按钮 -->
            <button
              on:click={handleValidation}
              disabled={validating || !univerAPI}
              class="w-full flex justify-center items-center py-4 px-6 border border-transparent text-base font-semibold rounded-xl text-white bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
            >
              {#if validating}
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                验证中...
              {:else}
                🚀 提交任务
                <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              {/if}
            </button>
          </div>
        </div>
      </div>

      <!-- Excel练习区域 -->
      <div class="lg:col-span-2 relative">
        <div class="bg-white shadow-xl rounded-2xl p-6 border border-gray-100 h-full flex flex-col">
          <!-- Excel练习区头部 -->
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center shadow-md">
                <span class="text-white text-sm">📊</span>
              </div>
              <h2 class="text-lg font-bold text-gray-900">
                练习区
              </h2>
            </div>
          </div>

          <!-- Excel工作区 -->
          <div class="border-2 border-gray-200 rounded-xl overflow-hidden shadow-inner bg-gray-50 flex-1">
            <UniverWrapper
              onReady={handleUniverReady}
              {initialData}
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
