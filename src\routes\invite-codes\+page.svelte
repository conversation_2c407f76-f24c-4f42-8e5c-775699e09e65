<script lang="ts">
  import { onMount } from 'svelte'

  export let data

  const { session, inviteCodes } = data
  
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      alert('邀请码已复制到剪贴板')
    })
  }
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  // 确保页面加载时滚动到顶部
  onMount(() => {
    window.scrollTo(0, 0)
  })
</script>

<svelte:head>
  <title>我的邀请码 - Excel学习平台</title>
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50" style="padding-top: 6rem;">
  <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
    <!-- 页面头部 -->
    <div class="bg-white shadow-xl rounded-2xl p-8 mb-8 border border-gray-100">
      <div class="flex items-center space-x-4 mb-6">
        <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg">
          <span class="text-white text-2xl">🎫</span>
        </div>
        <div>
          <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">
            我的邀请码
          </h1>
          <p class="text-gray-600">
            管理您的好友邀请码，邀请朋友一起学习Excel
          </p>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-200">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-gray-700">用户类型</span>
            <span class="text-blue-600">👤</span>
          </div>
          <div class="text-xl font-bold text-blue-700">
            {session.user.userType === 'beta' ? '内测用户' :
             session.user.userType === 'friend' ? '好友用户' : '普通用户'}
          </div>
        </div>

        <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-200">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-gray-700">经验值</span>
            <span class="text-green-600">⭐</span>
          </div>
          <div class="text-xl font-bold text-green-700">
            {session.user.score}
          </div>
        </div>

        <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-4 border border-purple-200">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-gray-700">邀请码数量</span>
            <span class="text-purple-600">🎯</span>
          </div>
          <div class="text-xl font-bold text-purple-700">
            {inviteCodes.codes.length}
          </div>
        </div>
      </div>
    </div>

    <!-- 邀请码列表 -->
    <div class="bg-white shadow-xl rounded-2xl border border-gray-100 overflow-hidden">
      <div class="p-6 border-b border-gray-200">
        <h2 class="text-xl font-bold text-gray-900 flex items-center space-x-2">
          <span>🎫</span>
          <span>好友邀请码列表</span>
        </h2>
        <p class="text-gray-600 mt-2">
          分享这些邀请码给朋友，让他们也能享受完整的学习体验
        </p>
      </div>

      {#if inviteCodes.codes.length === 0}
        <div class="p-8 text-center">
          <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-gray-400 text-2xl">📭</span>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">暂无邀请码</h3>
          <p class="text-gray-600 mb-4">
            {session.user.userType === 'normal' 
              ? '当您的经验值达到50分时，将自动获得3个好友邀请码'
              : '您还没有获得任何好友邀请码'
            }
          </p>
          {#if session.user.userType === 'normal' && session.user.score < 50}
            <div class="bg-blue-50 rounded-lg p-4 inline-block">
              <p class="text-blue-700 text-sm">
                还需要 <span class="font-bold">{50 - session.user.score}</span> 经验值即可获得邀请码
              </p>
            </div>
          {/if}
        </div>
      {:else}
        <div class="divide-y divide-gray-200">
          {#each inviteCodes.codes as code, index}
            <div class="p-4 sm:p-6 hover:bg-gray-50 transition-colors duration-200">
              <div class="flex items-center justify-between gap-2">
                <div class="flex items-center space-x-2 sm:space-x-4 min-w-0 flex-1">
                  <div class="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-sm sm:text-base flex-shrink-0">
                    {index + 1}
                  </div>
                  <div class="min-w-0 flex-1">
                    <div>
                      <span class="text-sm sm:text-lg font-mono font-bold text-gray-900 bg-gray-100 px-2 sm:px-3 py-1 rounded-lg select-all cursor-pointer break-all">
                        {code.code}
                      </span>
                    </div>
                    <div class="mt-1 text-xs sm:text-sm text-gray-500">
                      创建时间：{formatDate(code.createdAt)}
                      {#if code.isUsed && code.usedAt}
                        | 使用时间：{formatDate(code.usedAt)}
                      {/if}
                    </div>
                  </div>
                </div>
                
                <div class="flex items-center space-x-2 sm:space-x-3 flex-shrink-0">
                  {#if code.isUsed}
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      已使用
                    </span>
                  {:else}
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      可用
                    </span>
                    <button
                      on:click={() => copyToClipboard(code.code)}
                      class="inline-flex items-center px-2 sm:px-3 py-1 border border-transparent text-xs sm:text-sm font-medium rounded-md text-white bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200"
                    >
                      复制
                    </button>
                  {/if}
                </div>
              </div>
            </div>
          {/each}
        </div>
      {/if}
    </div>

    <!-- 使用说明 -->
    <div class="bg-blue-50 rounded-2xl p-6 mt-8 border border-blue-200">
      <h3 class="text-lg font-semibold text-blue-900 mb-3 flex items-center space-x-2">
        <span>💡</span>
        <span>使用说明</span>
      </h3>
      <div class="space-y-2 text-blue-800">
        <p>• 每个邀请码只能使用一次</p>
        <p>• 朋友使用您的邀请码注册后，可以解锁"进阶操作"和"实用技巧"关卡</p>
        <p>• 内测用户注册时自动获得5个好友邀请码</p>
        <p>• 普通用户达到50经验值时自动获得3个好友邀请码</p>
        <p>• 点击邀请码可选中文本，然后使用 Ctrl+C (或 Cmd+C) 复制</p>
      </div>
    </div>
  </div>
</div>
