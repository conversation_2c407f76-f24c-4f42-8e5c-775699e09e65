const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createTestUser() {
  try {
    // 删除已存在的测试用户
    await prisma.user.deleteMany({
      where: {
        email: '<EMAIL>'
      }
    })

    // 创建新的测试用户
    const hashedPassword = await bcrypt.hash('password123', 12)
    
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'testuser',
        password: hashedPassword,
        emailVerified: true, // 设置为已验证
        userType: 'normal',
        score: 0
      }
    })

    console.log('测试用户创建成功:', user)
  } catch (error) {
    console.error('创建测试用户失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestUser()
