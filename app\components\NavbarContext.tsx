'use client'

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react'

interface NavbarContextType {
  completedCount?: number
  totalCount?: number
  showLevelList?: boolean
  levelListHref?: string
  setNavbarData: (data: { completedCount?: number; totalCount?: number; showLevelList?: boolean; levelListHref?: string }) => void
}

const NavbarContext = createContext<NavbarContextType | undefined>(undefined)

export function NavbarProvider({ children }: { children: ReactNode }) {
  const [navbarData, setNavbarDataState] = useState<{
    completedCount?: number
    totalCount?: number
    showLevelList?: boolean
    levelListHref?: string
  }>({})

  const setNavbarData = useCallback((data: { completedCount?: number; totalCount?: number; showLevelList?: boolean; levelListHref?: string }) => {
    setNavbarDataState(prevData => ({ ...prevData, ...data }))
  }, [])

  return (
    <NavbarContext.Provider value={{
      ...navbarData,
      setNavbarData
    }}>
      {children}
    </NavbarContext.Provider>
  )
}

export function useNavbar() {
  const context = useContext(NavbarContext)
  if (context === undefined) {
    throw new Error('useNavbar must be used within a NavbarProvider')
  }
  return context
}
