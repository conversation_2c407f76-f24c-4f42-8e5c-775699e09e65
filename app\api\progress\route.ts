import { NextRequest, NextResponse } from 'next/server'
import { log } from '@/app/lib/logger';
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/app/lib/auth'
import { prisma } from '@/app/lib/db'
import { allocateFriendCodes } from '@/app/lib/invite-codes'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const { levelId, completed, score } = await request.json()

    if (!levelId) {
      return NextResponse.json(
        { error: '关卡ID是必需的' },
        { status: 400 }
      )
    }

    // 获取关卡信息
    const level = await prisma.level.findUnique({
      where: { id: levelId }
    })

    if (!level) {
      return NextResponse.json(
        { error: '关卡不存在' },
        { status: 404 }
      )
    }

    // 先检查用户是否已经完成过这个关卡
    const existingProgress = await prisma.userProgress.findUnique({
      where: {
        userId_levelId: {
          userId: session.user.id,
          levelId: levelId
        }
      }
    })

    // 判断是否是首次完成
    const isFirstTimeCompletion = completed && (!existingProgress || !existingProgress.completed)

    // 更新或创建用户进度
    const progress = await prisma.userProgress.upsert({
      where: {
        userId_levelId: {
          userId: session.user.id,
          levelId: levelId
        }
      },
      update: {
        completed: completed || false,
        score: score || 0,
        attempts: {
          increment: 1
        },
        completedAt: completed ? new Date() : null
      },
      create: {
        userId: session.user.id,
        levelId: levelId,
        completed: completed || false,
        score: score || 0,
        attempts: 1,
        completedAt: completed ? new Date() : null
      }
    })

    // 只有在首次完成关卡时才更新用户总积分
    if (isFirstTimeCompletion) {
      // 获取用户当前信息
      const currentUser = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { score: true, userType: true }
      })

      const oldScore = currentUser?.score || 0
      const newScore = oldScore + level.points

      // 更新用户积分
      await prisma.user.update({
        where: { id: session.user.id },
        data: {
          score: {
            increment: level.points
          }
        }
      })

      log.debug(`用户 ${session.user.id} 首次完成关卡 ${levelId}，增加 ${level.points} 积分`)

      // 检查是否需要分配好友邀请码（普通用户和邀请用户都可以获得）
      if ((currentUser?.userType === 'normal' || currentUser?.userType === 'friend') && oldScore < 50 && newScore >= 50) {
        try {
          await allocateFriendCodes(session.user.id, 3)
          log.debug(`用户 ${session.user.id} (${currentUser.userType}) 达到50经验值，自动分配了3个好友邀请码`)
        } catch (error) {
          log.error('自动分配好友邀请码失败:', error)
          // 不影响主流程，只记录错误
        }
      }
    } else if (completed) {
      log.debug(`用户 ${session.user.id} 重复完成关卡 ${levelId}，不增加积分`)
    }

    return NextResponse.json(progress)
  } catch (error) {
    log.error('更新进度错误:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const progress = await prisma.userProgress.findMany({
      where: {
        userId: session.user.id
      },
      include: {
        level: true
      },
      orderBy: {
        level: {
          order: 'asc'
        }
      }
    })

    return NextResponse.json(progress)
  } catch (error) {
    log.error('获取进度错误:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}