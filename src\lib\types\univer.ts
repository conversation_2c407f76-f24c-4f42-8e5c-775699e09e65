import type { Univer } from '@univerjs/core'
import type { FUniver } from '@univerjs/facade'

export interface UniverInstance extends Univer {}

export interface UniverAPI extends ReturnType<typeof FUniver.newAPI> {}

export type UniverReadyCallback = (instance: UniverInstance, api: UniverAPI) => void

export interface ValidationRule {
  type: string
  cell?: string
  range?: string
  expectedValue?: unknown
  condition?: string
  value?: unknown
  expectedBackgroundColor?: string
  expectedFontColor?: string
  expectedBorder?: string
  expectedAlignment?: string
  expectedFormat?: string
  expectedFormula?: string
  expectedDataValidation?: unknown
  expectedConditionalFormat?: unknown
  expectedPivotTable?: unknown
  expectedChart?: unknown
  [key: string]: unknown
}

export interface ValidationResult {
  success: boolean
  message: string
  details?: Record<string, unknown>
}
