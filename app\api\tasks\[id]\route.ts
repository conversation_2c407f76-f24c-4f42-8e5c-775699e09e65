import { NextRequest, NextResponse } from 'next/server'
import { log } from '@/app/lib/logger';
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/app/lib/auth'
import { prisma } from '@/app/lib/db'
import { hasAdvancedAccess } from '@/app/lib/invite-codes'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const resolvedParams = await params
    const taskId = resolvedParams.id

    // 获取任务详情
    const task = await prisma.task.findUnique({
      where: {
        id: taskId
      },
      include: {
        level: {
          include: {
            progress: {
              where: {
                user: {
                  email: session.user.email
                }
              }
            },
            parent: true
          }
        }
      }
    })

    if (!task) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 })
    }

    // 获取用户信息以检查权限
    const user = await prisma.user.findUnique({
      where: { email: session.user.email! },
      select: { userType: true, score: true }
    })

    // 检查是否是受限关卡
    const parentName = task.level.parent?.name || task.level.name
    const hasAccess = hasAdvancedAccess(user?.userType || 'normal', user?.score || 0, parentName)

    if (!hasAccess) {
      const errorMessage = user?.userType === 'normal'
        ? (parentName === '进阶操作'
          ? '您需要达到500经验值才能访问此任务'
          : '您需要达到600经验值才能访问此任务')
        : (user?.userType === 'friend' && parentName === '实用技巧'
          ? '您需要达到600经验值才能访问此任务'
          : '您没有权限访问此任务，请使用邀请码注册以获得访问权限')

      return NextResponse.json(
        { error: errorMessage },
        { status: 403 }
      )
    }

    return NextResponse.json({
      task,
      level: task.level
    })
  } catch (error) {
    log.error('Error fetching task:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}