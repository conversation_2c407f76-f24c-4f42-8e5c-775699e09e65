import { redirect } from '@sveltejs/kit'
import type { PageServerLoad } from './$types'

export const load: PageServerLoad = async ({ locals, fetch }) => {
  const session = await locals.auth()
  
  // 检查是否已登录
  if (!session?.user) {
    throw redirect(302, '/auth/signin')
  }
  
  try {
    // 获取关卡数据
    const response = await fetch('/api/levels')
    
    if (!response.ok) {
      throw new Error('Failed to fetch levels')
    }
    
    const levels = await response.json()
    
    return {
      session,
      levels
    }
  } catch (error) {
    console.error('Error loading dashboard data:', error)
    
    return {
      session,
      levels: []
    }
  }
}
