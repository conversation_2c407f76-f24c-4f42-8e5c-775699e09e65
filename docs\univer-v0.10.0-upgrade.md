# Univer v0.10.0 升级指南

## 概述

本文档记录了将Univer从之前版本升级到v0.10.0的过程，主要涉及新的插件注册API的使用。

## 主要变更

### 1. 插件注册API增强

Univer v0.10.0 对 `univer.registerPlugin` 方法进行了增强，现在支持一次性注册多个插件，无需多次循环调用，极大提升了插件注册的灵活性和效率。

### 2. 新的批量注册语法

根据官方文档，新的API支持以下语法：

```typescript
// 新的批量注册方式
univer.registerPlugin(
  UniverRenderEnginePlugin,
  UniverFormulaEnginePlugin,
  [UniverUIPlugin, {
    container: 'app',
  }],
  UniverDocsPlugin,
  UniverDocsUIPlugin,
  UniverSheetsPlugin,
  UniverSheetsUIPlugin,
);
```

## 实现策略

### 1. 向后兼容

为了确保升级的平滑性，我们采用了渐进式升级策略：

1. **尝试新API**: 首先尝试使用新的批量注册API
2. **回退机制**: 如果新API不支持或出现错误，自动回退到原有的逐个注册方式
3. **错误处理**: 添加详细的错误日志，便于调试和监控

### 2. 代码结构

#### 核心插件注册
核心插件也尝试使用新的批量注册API，并提供回退机制：

```typescript
// 核心插件 - 使用新的批量注册方式
try {
  // 尝试使用数组形式的批量注册
  const corePlugins = [
    UniverRenderEnginePlugin,
    UniverFormulaEnginePlugin,
    [UniverUIPlugin, { container: containerRef.current! }],
    UniverDocsPlugin,
    UniverDocsUIPlugin,
    UniverSheetsPlugin,
    UniverSheetsUIPlugin,
  ];

  // 使用类型断言绕过TypeScript检查
  (univer.registerPlugin as any)(...corePlugins);
  log.debug('核心插件使用新的批量注册API成功');
} catch (error) {
  // 回退到逐个注册
  log.warn('核心插件批量注册失败，回退到逐个注册:', error);
  univer.registerPlugin(UniverRenderEnginePlugin);
  univer.registerPlugin(UniverFormulaEnginePlugin);
  univer.registerPlugin(UniverUIPlugin, { container: containerRef.current! });
  // ... 其他核心插件
}
```

#### 懒加载插件注册
懒加载插件使用新的批量注册API，并提供回退机制：

```typescript
// 尝试使用新的批量注册API
try {
  const batchPlugins = lazy.getLazyPluginsBatch();
  univerInstanceRef.current.registerPlugin(...batchPlugins);
  log.debug('使用新的批量注册API成功');
} catch (error) {
  // 回退到逐个注册
  log.warn('批量注册失败，回退到逐个注册:', error);
  const plugins = lazy.default();
  plugins.forEach((p) => {
    univerInstanceRef.current.registerPlugin(p[0], p[1]);
  });
}
```

### 3. 插件配置格式

#### 新增批量注册函数

在 `lazy.ts` 中新增了专门用于批量注册的函数：

```typescript
// 新的批量注册格式 - 支持v0.10.0的新API
export function getLazyPluginsBatch() {
    return [
        UniverSheetsFormulaPlugin,
        UniverSheetsFormulaUIPlugin,
        UniverSheetsNumfmtPlugin,
        UniverSheetsNumfmtUIPlugin,
        UniverDataValidationPlugin,
        UniverSheetsDataValidationPlugin,
        [UniverSheetsDataValidationUIPlugin, {
            showEditOnDropdown: false
        }],
        // ... 其他插件
    ];
}
```

#### 保持原有格式

原有的数组格式函数保持不变，用于回退机制：

```typescript
export default function getLazyPlugins(): Array<[PluginCtor<Plugin>] | [PluginCtor<Plugin>, unknown]> {
    return [
        [UniverSheetsFormulaPlugin],
        [UniverSheetsFormulaUIPlugin],
        // ... 其他插件
    ];
}
```

## 优势

### 1. 性能提升
- 减少了多次函数调用的开销
- 批量注册可能在内部进行优化

### 2. 代码简洁
- 减少了循环代码
- 插件注册逻辑更加清晰

### 3. 向后兼容
- 保持了与旧版本的兼容性
- 渐进式升级，降低风险

## 测试验证

### 1. 功能测试
- 验证所有插件是否正常加载
- 确认Excel功能完整性

### 2. 性能测试
- 对比升级前后的加载时间
- 监控内存使用情况

### 3. 错误处理测试
- 模拟新API失败场景
- 验证回退机制是否正常工作

## 技术细节

### TypeScript类型支持
目前Univer v0.10.0的TypeScript类型定义可能还未完全支持新的批量注册语法，因此我们使用了类型断言来绕过编译检查：

```typescript
// 使用类型断言绕过TypeScript检查
(univer.registerPlugin as any)(...corePlugins);
```

这是一个临时解决方案，等Univer官方更新类型定义后可以移除类型断言。

### 运行时检测
代码在运行时会自动检测新API是否可用：
- 如果新API工作正常，会在控制台输出成功日志
- 如果新API失败，会自动回退到原有方式，并输出警告日志

## 注意事项

1. **监控日志**: 升级后需要密切关注控制台日志，确认是否成功使用新API
2. **性能监控**: 观察插件加载性能是否有改善
3. **功能验证**: 确保所有Excel功能正常工作
4. **错误处理**: 如果发现新API有问题，可以临时禁用批量注册，完全使用回退机制
5. **类型警告**: 目前会有ESLint关于使用`any`类型的警告，这是预期的，等官方类型定义更新后可以解决

## 后续计划

1. **监控期**: 升级后1-2周内密切监控
2. **优化**: 根据实际使用情况进一步优化批量注册逻辑
3. **清理**: 确认新API稳定后，可以考虑移除回退代码
