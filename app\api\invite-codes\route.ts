import { NextResponse } from 'next/server'
import { log } from '@/app/lib/logger';
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/app/lib/auth'
import { prisma } from '@/app/lib/db'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    // 获取用户的好友邀请码
    const friendCodes = await prisma.friendInviteCode.findMany({
      where: {
        ownerId: session.user.id
      },
      orderBy: {
        createdAt: 'asc'
      },
      select: {
        id: true,
        code: true,
        isUsed: true,
        usedBy: true,
        usedAt: true,
        createdAt: true
      }
    })

    return NextResponse.json({
      codes: friendCodes,
      total: friendCodes.length,
      used: friendCodes.filter(code => code.isUsed).length,
      available: friendCodes.filter(code => !code.isUsed).length
    })
  } catch (error) {
    log.error('获取邀请码错误:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
