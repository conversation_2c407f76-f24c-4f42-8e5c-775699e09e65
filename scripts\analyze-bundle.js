#!/usr/bin/env node

/**
 * Bundle分析脚本
 * 分析构建产物，识别性能瓶颈
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 开始分析Bundle...\n');

// 1. 构建并分析
console.log('📦 构建项目...');
try {
  execSync('pnpm build:analyze', { stdio: 'inherit' });
} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
}

// 2. 分析.next目录
const nextDir = path.join(process.cwd(), '.next');
const staticDir = path.join(nextDir, 'static');

if (!fs.existsSync(staticDir)) {
  console.error('❌ 找不到构建产物目录');
  process.exit(1);
}

// 3. 分析chunk大小
console.log('\n📊 分析Chunk大小...');

function getDirectorySize(dirPath) {
  let totalSize = 0;
  
  function calculateSize(currentPath) {
    const stats = fs.statSync(currentPath);
    
    if (stats.isDirectory()) {
      const files = fs.readdirSync(currentPath);
      files.forEach(file => {
        calculateSize(path.join(currentPath, file));
      });
    } else {
      totalSize += stats.size;
    }
  }
  
  calculateSize(dirPath);
  return totalSize;
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 分析各个目录
const chunksDir = path.join(staticDir, 'chunks');
if (fs.existsSync(chunksDir)) {
  const chunks = fs.readdirSync(chunksDir);
  const chunkSizes = [];
  
  chunks.forEach(chunk => {
    const chunkPath = path.join(chunksDir, chunk);
    const stats = fs.statSync(chunkPath);
    
    if (stats.isFile() && chunk.endsWith('.js')) {
      chunkSizes.push({
        name: chunk,
        size: stats.size,
        formatted: formatBytes(stats.size)
      });
    }
  });
  
  // 按大小排序
  chunkSizes.sort((a, b) => b.size - a.size);
  
  console.log('\n🎯 最大的JavaScript Chunks:');
  console.log('=' .repeat(60));
  chunkSizes.slice(0, 10).forEach((chunk, index) => {
    const indicator = chunk.size > 500000 ? '🔴' : chunk.size > 200000 ? '🟡' : '🟢';
    console.log(`${index + 1}. ${indicator} ${chunk.name} - ${chunk.formatted}`);
  });
}

// 4. 检查Univer相关文件
console.log('\n🔍 Univer相关文件分析:');
console.log('=' .repeat(60));

function findUniverFiles(dir, results = []) {
  if (!fs.existsSync(dir)) return results;
  
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      findUniverFiles(filePath, results);
    } else if (file.includes('univer') && (file.endsWith('.js') || file.endsWith('.css'))) {
      results.push({
        name: file,
        path: filePath,
        size: stats.size,
        formatted: formatBytes(stats.size)
      });
    }
  });
  
  return results;
}

const univerFiles = findUniverFiles(staticDir);
univerFiles.sort((a, b) => b.size - a.size);

if (univerFiles.length > 0) {
  univerFiles.forEach((file, index) => {
    const type = file.name.endsWith('.css') ? '🎨' : '📜';
    console.log(`${index + 1}. ${type} ${file.name} - ${file.formatted}`);
  });
} else {
  console.log('ℹ️  未找到明确标识的Univer文件（可能被打包到其他chunk中）');
}

// 5. 生成优化建议
console.log('\n💡 优化建议:');
console.log('=' .repeat(60));

const totalSize = getDirectorySize(staticDir);
console.log(`📦 总构建大小: ${formatBytes(totalSize)}`);

if (totalSize > 5 * 1024 * 1024) { // 5MB
  console.log('🔴 构建产物较大，建议优化:');
  console.log('   • 检查是否有重复依赖');
  console.log('   • 考虑进一步拆分代码');
  console.log('   • 启用gzip压缩');
} else if (totalSize > 2 * 1024 * 1024) { // 2MB
  console.log('🟡 构建产物中等，可以进一步优化:');
  console.log('   • 检查大型依赖是否必要');
  console.log('   • 考虑懒加载更多模块');
} else {
  console.log('🟢 构建产物大小合理');
}

// 6. 检查是否有source map
const hasSourceMaps = fs.readdirSync(staticDir).some(file => 
  file.includes('.map')
);

if (hasSourceMaps) {
  console.log('⚠️  检测到source map文件，确保生产环境已禁用');
}

console.log('\n✅ 分析完成！');
console.log('💡 运行 "pnpm build:analyze" 可以查看详细的bundle分析报告');
