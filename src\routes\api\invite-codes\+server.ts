import { json } from '@sveltejs/kit'
import type { RequestHandler } from './$types'
import { prisma } from '$lib/db'
import { log } from '$lib/logger'

export const GET: RequestHandler = async ({ locals }) => {
  try {
    const session = await locals.auth()
    
    if (!session?.user) {
      return json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    // 获取用户的好友邀请码
    const friendCodes = await prisma.friendInviteCode.findMany({
      where: {
        ownerId: session.user.id
      },
      orderBy: {
        createdAt: 'asc'
      },
      select: {
        id: true,
        code: true,
        isUsed: true,
        usedBy: true,
        usedAt: true,
        createdAt: true
      }
    })

    return json({
      codes: friendCodes,
      total: friendCodes.length,
      used: friendCodes.filter(code => code.isUsed).length,
      available: friendCodes.filter(code => !code.isUsed).length
    })
  } catch (error) {
    log.error('获取邀请码错误:', error)
    return json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
