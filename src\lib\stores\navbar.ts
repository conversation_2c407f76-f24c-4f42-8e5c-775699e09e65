import { writable } from 'svelte/store'

interface NavbarData {
  completedCount?: number
  totalCount?: number
  showLevelList?: boolean
  levelListHref?: string
}

function createNavbarStore() {
  const { subscribe, set, update } = writable<NavbarData>({})

  return {
    subscribe,
    setNavbarData: (data: NavbarData) => update(current => ({ ...current, ...data })),
    reset: () => set({})
  }
}

export const navbarStore = createNavbarStore()
