import { NextRequest, NextResponse } from 'next/server'
import { log } from '@/app/lib/logger'
import bcrypt from 'bcryptjs'
import { prisma } from '@/app/lib/db'

export async function POST(request: NextRequest) {
  try {
    const { token, password } = await request.json()

    if (!token || !password) {
      return NextResponse.json(
        { error: '令牌和新密码都是必填的' },
        { status: 400 }
      )
    }

    // 验证密码强度
    if (password.length < 6) {
      return NextResponse.json(
        { error: '密码长度至少为6位' },
        { status: 400 }
      )
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: {
        passwordResetToken: token
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: '无效的重置令牌' },
        { status: 400 }
      )
    }

    // 检查令牌是否过期
    if (user.passwordResetExpires && user.passwordResetExpires < new Date()) {
      return NextResponse.json(
        { error: '重置令牌已过期，请重新申请密码重置' },
        { status: 400 }
      )
    }

    // 加密新密码
    const hashedPassword = await bcrypt.hash(password, 12)

    // 更新用户密码并清除重置令牌
    await prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        passwordResetToken: null,
        passwordResetExpires: null
      }
    })

    log.validation('密码重置成功:', user.email)

    return NextResponse.json(
      { message: '密码重置成功！您现在可以使用新密码登录了。' },
      { status: 200 }
    )
  } catch (error) {
    log.error('密码重置错误:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// 验证重置令牌的GET端点
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get('token')

    if (!token) {
      return NextResponse.json(
        { error: '重置令牌缺失' },
        { status: 400 }
      )
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: {
        passwordResetToken: token
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: '无效的重置令牌' },
        { status: 400 }
      )
    }

    // 检查令牌是否过期
    if (user.passwordResetExpires && user.passwordResetExpires < new Date()) {
      return NextResponse.json(
        { error: '重置令牌已过期，请重新申请密码重置' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: '令牌有效', email: user.email },
      { status: 200 }
    )
  } catch (error) {
    log.error('令牌验证错误:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
