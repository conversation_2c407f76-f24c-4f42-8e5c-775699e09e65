import { NextRequest, NextResponse } from 'next/server'
import { log } from '@/app/lib/logger';
import { prisma } from '@/app/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get('token')

    if (!token) {
      return NextResponse.json(
        { error: '验证令牌缺失' },
        { status: 400 }
      )
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: {
        emailVerificationToken: token
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: '无效的验证令牌' },
        { status: 400 }
      )
    }

    // 检查令牌是否过期
    if (user.emailVerificationExpires && user.emailVerificationExpires < new Date()) {
      return NextResponse.json(
        { error: '验证令牌已过期，请重新注册' },
        { status: 400 }
      )
    }

    // 检查邮箱是否已经验证
    if (user.emailVerified) {
      return NextResponse.json(
        { message: '邮箱已经验证过了' },
        { status: 200 }
      )
    }

    // 更新用户状态
    await prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerified: true,
        emailVerificationToken: null,
        emailVerificationExpires: null
      }
    })

    return NextResponse.json(
      { message: '邮箱验证成功！您现在可以登录了。' },
      { status: 200 }
    )
  } catch (error) {
    log.error('邮箱验证错误:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
