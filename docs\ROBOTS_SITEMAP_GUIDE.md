# 动态 robots.txt 和 sitemap.xml 实现指南

## 概述

本项目实现了动态生成 `robots.txt` 和 `sitemap.xml` 的功能，通过 Next.js API 路由根据环境变量和数据库内容自动生成这些 SEO 重要文件。

## 文件结构

```
app/
├── robots.txt/
│   └── route.ts          # 动态生成 robots.txt
├── sitemap.xml/
│   └── route.ts          # 动态生成 sitemap.xml
└── ...
```

## robots.txt 功能

### 访问地址
- 开发环境: `http://localhost:3000/robots.txt`
- 生产环境: `https://your-domain.com/robots.txt`

### 功能特性

1. **环境感知**
   - 开发环境：禁止所有爬虫访问 (`Disallow: /`)
   - 生产环境：允许爬虫访问 (`Allow: /`)

2. **搜索引擎优化**
   - 特别针对百度爬虫 (Baiduspider) 优化
   - 特别针对 Bing 爬虫 (bingbot) 优化
   - 支持 Google 爬虫 (Googlebot)
   - 设置合理的爬取延迟 (Crawl-delay: 1)

3. **路径控制**
   - 禁止访问：`/api/`, `/auth/`, `/_next/`, `/admin/`, `/profile/`, `/dashboard/`, `/task/`, `/level/`, `/invite-codes/`
   - 允许访问：`/legal/`, `/`

4. **动态 URL**
   - 自动从环境变量 `NEXT_PUBLIC_BASE_URL` 获取基础 URL
   - 如果环境变量未设置，则从请求头动态获取

### 示例输出

```
User-agent: *
Allow: /

# 特别针对百度爬虫的优化
User-agent: Baiduspider
Allow: /
Crawl-delay: 1

# 特别针对Bing爬虫的优化  
User-agent: bingbot
Allow: /
Crawl-delay: 1

# Google爬虫优化
User-agent: Googlebot
Allow: /

# 禁止访问的路径（所有爬虫）
Disallow: /api/
Disallow: /auth/
Disallow: /_next/
Disallow: /admin/
Disallow: /profile/
Disallow: /dashboard/
Disallow: /task/
Disallow: /level/
Disallow: /invite-codes/

# 允许访问的公共路径
Allow: /legal/
Allow: /

# 网站地图位置
Sitemap: https://your-domain.com/sitemap.xml
```

## sitemap.xml 功能

### 访问地址
- 开发环境: `http://localhost:3000/sitemap.xml`
- 生产环境: `https://your-domain.com/sitemap.xml`

### 功能特性

1. **动态内容生成**
   - 自动从数据库获取所有关卡 (Level) 和任务 (Task)
   - 为每个关卡和任务生成对应的 URL

2. **页面优先级设置**
   - 首页：优先级 1.0，每日更新
   - 关卡页面：优先级 0.8，每周更新
   - 任务页面：优先级 0.7，每周更新
   - 法律页面：优先级 0.3，每月更新

3. **错误处理**
   - 如果数据库查询失败，返回基础 sitemap
   - 确保服务的可用性

4. **缓存优化**
   - 设置 1 小时缓存 (`Cache-Control: public, max-age=3600, s-maxage=3600`)

### 包含的页面类型

1. **静态页面**
   - 首页 (`/`)
   - 隐私政策 (`/legal/privacy-policy`)
   - 服务条款 (`/legal/terms-of-service`)

2. **动态页面**
   - 关卡页面 (`/level/{id}`)
   - 任务页面 (`/task/{id}`)

## 环境变量配置

### 自动 URL 检测

当前实现使用动态 URL 检测，自动从请求头获取正确的域名和端口，无需手动配置环境变量。

### 可选的环境变量

```bash
# 开发环境
NODE_ENV=development

# 生产环境
NODE_ENV=production

# 可选：手动指定基础URL（如果需要覆盖自动检测）
NEXT_PUBLIC_BASE_URL="https://your-domain.com"
```

### URL 获取逻辑

```typescript
// 动态获取基础URL，从请求头获取当前域名和端口
const url = new URL(req.url)
const baseUrl = `${url.protocol}//${url.host}`
```

这种方式的优势：
- 自动适应不同的端口和域名
- 无需手动配置环境变量
- 避免开发和生产环境的配置差异

## 部署注意事项

1. **环境变量设置**
   - 确保在生产环境中设置正确的 `NEXT_PUBLIC_BASE_URL`
   - 确保 `NODE_ENV` 设置为 `production`

2. **数据库连接**
   - 确保生产环境中数据库连接正常
   - sitemap.xml 依赖数据库中的关卡和任务数据

3. **缓存策略**
   - 两个文件都设置了 1 小时缓存
   - 可以根据需要调整缓存时间

## 测试方法

### 本地测试

```bash
# 启动开发服务器
npm run dev

# 测试 robots.txt
curl http://localhost:3000/robots.txt

# 测试 sitemap.xml
curl http://localhost:3000/sitemap.xml
```

### 生产环境测试

```bash
# 构建并启动生产版本
npm run build
npm start

# 测试文件
curl https://your-domain.com/robots.txt
curl https://your-domain.com/sitemap.xml
```

## SEO 最佳实践

1. **搜索引擎提交**
   - 在 Google Search Console 提交 sitemap
   - 在百度站长平台提交 sitemap
   - 在 Bing 站长工具提交 sitemap

2. **监控和维护**
   - 定期检查 robots.txt 和 sitemap.xml 的可访问性
   - 监控搜索引擎的爬取情况
   - 根据需要调整爬虫策略

3. **内容更新**
   - sitemap.xml 会自动包含新添加的关卡和任务
   - 无需手动维护 URL 列表

## 故障排除

### 常见问题

1. **sitemap.xml 只显示基础页面**
   - 检查数据库连接是否正常
   - 确认数据库中有关卡和任务数据
   - 查看服务器日志中的错误信息

2. **robots.txt 显示中文乱码**
   - 已修复：将中文注释改为英文注释
   - 添加了正确的字符编码头部 `Content-Type: text/plain; charset=utf-8`

3. **URL 显示错误的端口或域名**
   - 已修复：使用动态 URL 获取，从请求头自动获取正确的域名和端口
   - 不再依赖环境变量，避免缓存问题

4. **缓存问题**
   - 清除浏览器缓存
   - 等待缓存过期（1小时）
   - 或者重启服务器
   - 使用强制刷新（F5 或 Ctrl+F5）

### 调试方法

```bash
# 查看环境变量
echo $NEXT_PUBLIC_BASE_URL
echo $NODE_ENV

# 检查数据库连接
# 在应用中添加日志输出来调试数据库查询
```

## 相关文档

- [SEO优化指南](./SEO-OPTIMIZATION.md)
- [部署指南](./ubuntu-deployment-guide.md)
- [Next.js API Routes 文档](https://nextjs.org/docs/api-routes/introduction)
