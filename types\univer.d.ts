// Univer TypeScript definitions

// 单元格数据类型定义
export interface CellData {
  v: unknown; // 单元格值
  s?: Record<string, unknown>; // 样式数据
  f?: string; // 公式
  t?: number; // 数据类型
  p?: Record<string, unknown>; // 其他属性
}

// 工作表数据类型
export interface WorksheetData {
  id: string;
  name: string;
  cellData: Record<number, Record<number, CellData>>;
  rowCount?: number;
  columnCount?: number;
  [key: string]: unknown;
}

// 工作簿数据类型
export interface WorkbookData {
  id: string;
  locale: string;
  name: string;
  sheetOrder: string[];
  sheets: Record<string, WorksheetData>;
  [key: string]: unknown;
}

export interface UniverInstance {
  dispose?: () => void;
  getWorkbook?: () => any;
  getActiveSheet?: () => any;
  createUnit?: (type: any, data: WorkbookData) => void;
  registerPlugin?: (plugin: any, config?: any) => void;
  registerPlugins?: (plugins: any[]) => void;
  [key: string]: any;
}

export interface UniverWorkbook {
  getActiveSheet(): UniverWorksheet | null;
  getSheetBySheetId?(sheetId: string): UniverWorksheet | null;
  getSheetCount?(): number;
}

export interface UniverAPI {
  getActiveWorkbook(): UniverWorkbook | null;
  getSheetData?: () => any;
  setSheetData?: (data: any) => void;
  getCellValue?: (row: number, col: number) => any;
  setCellValue?: (row: number, col: number, value: any) => void;
  dispose?: () => void;
  [key: string]: any;
}

export interface UniverReadyCallback {
  (instance: UniverInstance, api: UniverAPI): void;
}

export interface UniverWorksheet {
  getSheetId(): string;
  getName(): string;
  getCellData(): Record<string, Record<string, CellData>>;
  getRange(range: string): UniverRange;
  getCell(row: number, col: number): CellData;
  getFilter(): unknown | null;
  // 兼容通过行列号获取范围
  getRangeByIndexes?(row: number, col: number): UniverRange;
}

export interface UniverRange {
  getValue(): unknown;
  getValues(): unknown[][];
  getFormula(): string;
  getFormulas(): string[][];
  getBackgroundColor(): string;
  getBackgroundColors(): string[][];
  getCellData(): CellData;
  getNumberFormat(): string;
  getDisplayValue(): string;
  getConditionalFormattingRules(): unknown[];
  isMerged(): boolean;
  getWrap(): boolean;
}