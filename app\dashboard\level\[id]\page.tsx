'use client'

import { useSession } from 'next-auth/react'
import { log } from '@/app/lib/logger';
import { useEffect, useState, useCallback } from 'react'
import { useRouter, useParams } from 'next/navigation'
import Link from 'next/link'

interface Task {
  id: string
  name: string
  description: string
  type: string
  order: number
  validation: string
  initialData?: string
}

interface Level {
  id: string
  name: string
  description: string
  difficulty: number
  order: number
  points: number
  parentId?: string
  tasks: Task[]
  progress: Array<{
    completed: boolean
    score: number
  }>
}

export default function LevelDetail() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const params = useParams()
  const [level, setLevel] = useState<Level | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  const fetchLevel = useCallback(async () => {
    try {
      const response = await fetch(`/api/levels/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setLevel(data)
      } else {
        log.error('获取关卡详情失败')
        router.push('/dashboard')
      }
    } catch (error) {
      log.error('获取关卡详情失败:', error)
      router.push('/dashboard')
    } finally {
      setLoading(false)
    }
  }, [params.id, router])

  useEffect(() => {
    if (session && params.id) {
      fetchLevel()
    }
  }, [session, params.id, fetchLevel])

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!session || !level) {
    return null
  }

  const isLevelCompleted = level.progress.length > 0 && level.progress[0].completed
  const levelScore = level.progress.length > 0 ? level.progress[0].score : 0

  const getDifficultyColor = (difficulty: number) => {
    switch (difficulty) {
      case 1:
        return 'bg-green-100 text-green-800'
      case 2:
        return 'bg-yellow-100 text-yellow-800'
      case 3:
        return 'bg-orange-100 text-orange-800'
      case 4:
        return 'bg-red-100 text-red-800'
      case 5:
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getDifficultyText = (difficulty: number) => {
    switch (difficulty) {
      case 1:
        return '入门'
      case 2:
        return '简单'
      case 3:
        return '中等'
      case 4:
        return '困难'
      case 5:
        return '专家'
      default:
        return '未知'
    }
  }

  const getTaskTypeText = (type: string) => {
    switch (type) {
      case 'formula':
        return '公式计算'
      case 'format':
        return '格式设置'
      case 'chart':
        return '图表制作'
      case 'pivot':
        return '数据透视表'
      case 'function':
        return '函数应用'
      default:
        return '综合练习'
    }
  }

  const getTaskTypeColor = (type: string) => {
    switch (type) {
      case 'formula':
        return 'bg-blue-100 text-blue-800'
      case 'format':
        return 'bg-purple-100 text-purple-800'
      case 'chart':
        return 'bg-green-100 text-green-800'
      case 'pivot':
        return 'bg-orange-100 text-orange-800'
      case 'function':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 sm:px-0">
            {/* 主任务信息 */}
            <div className="bg-white shadow rounded-lg mb-6">
              <div className="px-6 py-4">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">{level.name}</h1>
                    <p className="text-gray-600 mt-1">{level.description}</p>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                      getDifficultyColor(level.difficulty)
                    }`}>
                      {getDifficultyText(level.difficulty)}
                    </span>
                    {isLevelCompleted && (
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        ✓ 已完成
                      </span>
                    )}
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="text-sm text-gray-500">光卡数</div>
                    <div className="text-2xl font-bold text-gray-900">{level.tasks.length}</div>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="text-sm text-gray-500">奖励经验值</div>
                    <div className="text-2xl font-bold text-gray-900">{level.points}</div>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="text-sm text-gray-500">当前经验值</div>
                    <div className="text-2xl font-bold text-gray-900">{levelScore}</div>
                  </div>
                </div>
              </div>
            </div>

            {/* 子任务列表 */}
            <div className="mb-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">子任务列表</h2>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {level.tasks.map((task, index) => {
                  return (
                    <div
                      key={task.id}
                      className="bg-white overflow-hidden shadow rounded-lg border border-gray-200 hover:border-blue-300 transition-colors"
                    >
                      <div className="p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="text-lg font-medium text-gray-900">
                            {task.name}
                          </h3>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            getTaskTypeColor(task.type)
                          }`}>
                            {getTaskTypeText(task.type)}
                          </span>
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-4">
                          {task.description}
                        </p>
                        
                        <div className="flex items-center justify-between mb-4">
                          <span className="text-sm text-gray-500">
                            任务 {index + 1}
                          </span>
                        </div>
                        
                        <Link
                          href={`/task/${task.id}`}
                          className="w-full inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                        >
                          再来一次
                        </Link>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
        </div>
      </div>
    </div>
  )
}