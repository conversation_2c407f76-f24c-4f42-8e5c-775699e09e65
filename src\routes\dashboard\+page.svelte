<script lang="ts">
  import { onMount } from 'svelte'

  interface Task {
    id: string
    name: string
    description: string
    type: string
    order: number
    validation: string
    initialData?: string
  }

  interface SubLevel {
    id: string
    name: string
    description: string
    difficulty: number
    order: number
    points: number
    tasks: Task[]
    progress: Array<{
      completed: boolean
      score: number
    }>
  }

  interface MainTask {
    id: string
    name: string
    description: string
    difficulty: number
    order: number
    points: number
    isMainTask: boolean
    hasAccess: boolean
    isLocked: boolean
    buttonText?: string
    requiredScore?: number
    children: SubLevel[]
    progress: Array<{
      completed: boolean
      score: number
    }>
  }

  export let data

  const { session, levels } = data
  const mainTasks: MainTask[] = levels

  // 计算所有子任务的完成情况
  $: allSubLevels = mainTasks.flatMap(mainTask => mainTask.children)
  $: completedLevels = allSubLevels.filter(subLevel => subLevel.progress.length > 0).length
  $: totalPoints = allSubLevels.reduce((sum, subLevel) => {
    if (subLevel.progress.length > 0) {
      return sum + subLevel.points
    }
    return sum
  }, 0)

  const getDifficultyColor = (difficulty: number) => {
    switch (difficulty) {
      case 1:
        return 'bg-green-100 text-green-800'
      case 2:
        return 'bg-yellow-100 text-yellow-800'
      case 3:
        return 'bg-orange-100 text-orange-800'
      case 4:
        return 'bg-red-100 text-red-800'
      case 5:
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getDifficultyText = (difficulty: number) => {
    switch (difficulty) {
      case 1:
        return '入门'
      case 2:
        return '简单'
      case 3:
        return '中等'
      case 4:
        return '困难'
      case 5:
        return '专家'
      default:
        return '未知'
    }
  }

  // 确保页面加载时滚动到顶部
  onMount(() => {
    window.scrollTo(0, 0)
  })
</script>

<svelte:head>
  <title>学习进度 - Excel学习平台</title>
  <meta name="description" content="查看您的学习进度，选择任务开始学习之旅" />
</svelte:head>

{#if !session}
  <!-- 会被重定向到登录页面 -->
{:else}
  <div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50" style="padding-top: 6rem;">
    <div class="max-w-7xl mx-auto py-8 sm:px-6 lg:px-8">
      <div class="px-4 sm:px-0">
        <!-- 页面标题 -->
        <div class="mb-8 text-center">
          <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">
            学习进度
          </h1>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            选择一个任务开始你的学习之旅，每一步都是成长的足迹
          </p>
        </div>

        <!-- 用户进度概览 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
          <!-- 已闯关卡片 -->
          <div class="bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
            <div class="p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-14 h-14 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg">
                    <span class="text-white text-xl">🏆</span>
                  </div>
                </div>
                <div class="ml-4 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 mb-1">
                      已闯关
                    </dt>
                    <dd class="text-2xl font-bold text-gray-900">
                      {completedLevels}
                    </dd>
                    <dd class="text-sm text-gray-500">
                      / {allSubLevels.length} 关卡
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <!-- 总经验值卡片 -->
          <div class="bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
            <div class="p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-14 h-14 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg">
                    <span class="text-white text-xl">⭐</span>
                  </div>
                </div>
                <div class="ml-4 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 mb-1">
                      总经验值
                    </dt>
                    <dd class="text-2xl font-bold text-gray-900">
                      {totalPoints}
                    </dd>
                    <dd class="text-sm text-gray-500">
                      EXP
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <!-- 完成率卡片 -->
          <div class="bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
            <div class="p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-14 h-14 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg">
                    <span class="text-white text-xl">📊</span>
                  </div>
                </div>
                <div class="ml-4 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 mb-1">
                      完成率
                    </dt>
                    <dd class="text-2xl font-bold text-gray-900">
                      {allSubLevels.length > 0 ? Math.round((completedLevels / allSubLevels.length) * 100) : 0}%
                    </dd>
                    <dd class="text-sm text-gray-500">
                      学习进度
                    </dd>
                  </dl>
                </div>
              </div>
              <!-- 进度条 -->
              <div class="mt-4">
                <div class="bg-gray-200 rounded-full h-2">
                  <div
                    class="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-500"
                    style="width: {allSubLevels.length > 0 ? Math.round((completedLevels / allSubLevels.length) * 100) : 0}%"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 主任务列表 -->
        <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {#each mainTasks as mainTask}
            {@const completedSubLevels = mainTask.children.filter(subLevel =>
              subLevel.progress.length > 0 && subLevel.progress[0].completed
            ).length}
            {@const totalSubLevels = mainTask.children.length}
            {@const progressPercentage = totalSubLevels > 0 ? Math.round((completedSubLevels / totalSubLevels) * 100) : 0}
            {@const isMainTaskCompleted = mainTask.progress.length > 0 && mainTask.progress[0].completed}

            <!-- 计算已获积分 -->
            {@const earnedPoints = mainTask.children
              .filter(subLevel => subLevel.progress.length > 0 && subLevel.progress[0].completed)
              .reduce((sum, subLevel) => sum + subLevel.points, 0)}

            <!-- 判断关卡状态 -->
            {@const isFullyCompleted = progressPercentage === 100}
            {@const isPartiallyCompleted = progressPercentage > 0 && progressPercentage < 100}

            <!-- 获取按钮样式和文字 -->
            {@const buttonConfig = (() => {
              if (isFullyCompleted) {
                return {
                  className: "w-full inline-flex justify-center items-center py-3 px-6 border border-transparent rounded-xl text-sm font-semibold text-white bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200",
                  text: "✅ 已完成",
                  icon: "🏆"
                }
              } else if (isPartiallyCompleted) {
                return {
                  className: "w-full inline-flex justify-center items-center py-3 px-6 border border-transparent rounded-xl text-sm font-semibold text-white bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200",
                  text: "📚 继续学习",
                  icon: "⚡"
                }
              } else {
                return {
                  className: "w-full inline-flex justify-center items-center py-3 px-6 border border-transparent rounded-xl text-sm font-semibold text-white bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200",
                  text: "🚀 开始学习",
                  icon: "🎯"
                }
              }
            })()}

            <div
              class="bg-white overflow-hidden shadow-xl rounded-2xl border border-gray-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 relative {isMainTaskCompleted ? 'ring-2 ring-green-200 bg-gradient-to-br from-green-50 to-white' : mainTask.isLocked ? 'opacity-75 cursor-not-allowed' : 'hover:border-gray-200'}"
            >
              <!-- 锁定/解锁图标 -->
              <div class="absolute top-4 right-4 z-10">
                {#if mainTask.isLocked}
                  <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center shadow-lg">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>
                {:else if mainTask.hasAccess !== undefined}
                  <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center shadow-lg">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
                    </svg>
                  </div>
                {/if}
              </div>

              <!-- 卡片顶部装饰条 -->
              <div class="h-1 {isFullyCompleted ? 'bg-gradient-to-r from-green-500 to-emerald-500' : isPartiallyCompleted ? 'bg-gradient-to-r from-blue-500 to-indigo-500' : mainTask.isLocked ? 'bg-gradient-to-r from-red-400 to-red-500' : 'bg-gradient-to-r from-gray-400 to-gray-500'}"></div>

              <div class="p-8">
                <div class="flex items-start justify-between mb-6">
                  <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 rounded-xl flex items-center justify-center {isFullyCompleted ? 'bg-gradient-to-r from-green-500 to-emerald-500' : isPartiallyCompleted ? 'bg-gradient-to-r from-blue-500 to-indigo-500' : 'bg-gradient-to-r from-gray-500 to-gray-600'} shadow-lg">
                      <span class="text-white text-xl">
                        {isFullyCompleted ? '🏆' : isPartiallyCompleted ? '📚' : '🎯'}
                      </span>
                    </div>
                    <div>
                      <h2 class="text-xl font-bold text-gray-900 mb-1">
                        {mainTask.name}
                      </h2>
                      <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold {getDifficultyColor(mainTask.difficulty)}">
                        {getDifficultyText(mainTask.difficulty)}
                      </span>
                    </div>
                  </div>

                  {#if isMainTaskCompleted}
                    <div class="flex items-center space-x-1 text-green-600">
                      <span class="text-lg">✅</span>
                      <span class="text-sm font-medium">完成</span>
                    </div>
                  {/if}
                </div>

                <div class="mb-6 h-12 overflow-hidden">
                  <p class="text-gray-600 leading-6 text-lg" style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;">
                    {mainTask.description}
                  </p>
                </div>

                <!-- 统计信息网格 -->
                <div class="grid grid-cols-3 gap-4 mb-6">
                  <div class="text-center p-3 bg-gray-50 rounded-xl">
                    <div class="text-lg font-bold text-gray-900">{completedSubLevels}</div>
                    <div class="text-xs text-gray-500">已完成</div>
                  </div>
                  <div class="text-center p-3 bg-gray-50 rounded-xl">
                    <div class="text-lg font-bold text-gray-900">{totalSubLevels}</div>
                    <div class="text-xs text-gray-500">总关卡</div>
                  </div>
                  <div class="text-center p-3 bg-gray-50 rounded-xl">
                    <div class="text-lg font-bold text-blue-600">{earnedPoints}</div>
                    <div class="text-xs text-gray-500">经验值</div>
                  </div>
                </div>

                <!-- 进度条 -->
                <div class="mb-6">
                  <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">学习进度</span>
                    <span class="text-sm font-bold text-gray-900">{progressPercentage}%</span>
                  </div>
                  <div class="bg-gray-200 rounded-full h-3 overflow-hidden">
                    <div
                      class="h-3 rounded-full transition-all duration-500 {isFullyCompleted ? 'bg-gradient-to-r from-green-500 to-emerald-500' : isPartiallyCompleted ? 'bg-gradient-to-r from-blue-500 to-indigo-500' : 'bg-gradient-to-r from-gray-400 to-gray-500'}"
                      style="width: {progressPercentage}%"
                    ></div>
                  </div>
                </div>

                {#if mainTask.isLocked}
                  <div class="w-full px-2 sm:px-6 py-3 bg-gray-300 text-gray-500 rounded-xl text-center font-medium cursor-not-allowed">
                    <span class="flex items-center justify-center space-x-1 sm:space-x-2">
                      <svg class="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                      <span class="text-xs leading-tight whitespace-nowrap overflow-hidden text-ellipsis font-bold">{mainTask.buttonText || '需要邀请码解锁'}</span>
                    </span>
                  </div>
                {:else}
                  <a
                    href="/level/{mainTask.id}"
                    class="{mainTask.buttonText && mainTask.buttonText.includes('专属区域，已对您开放') ? 'w-full inline-flex justify-center items-center py-3 px-2 sm:px-6 border border-transparent rounded-xl font-semibold text-amber-900 bg-gradient-to-r from-amber-400 to-yellow-400 hover:from-amber-500 hover:to-yellow-500 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200' : buttonConfig.className}"
                  >
                    <span class="flex items-center space-x-1 sm:space-x-2">
                      <span class="text-xs leading-tight whitespace-nowrap overflow-hidden text-ellipsis font-bold">{mainTask.buttonText && (mainTask.name === '进阶操作' || mainTask.name === '实用技巧') ? mainTask.buttonText : buttonConfig.text}</span>
                      <svg class="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </span>
                  </a>
                {/if}
              </div>
            </div>
          {/each}
        </div>
      </div>
    </div>
  </div>
{/if}
