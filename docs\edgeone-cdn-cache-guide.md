# EdgeOne CDN缓存自动清除指南

## 概述

本指南介绍如何配置和使用EdgeOne CDN缓存自动清除功能，解决代码更新后缓存不更新的问题。

## 功能特性

- ✅ 构建完成后自动清除CDN缓存
- ✅ 支持全站缓存清除和指定URL清除
- ✅ 完整的错误处理和日志记录
- ✅ 腾讯云EdgeOne API集成
- ✅ 环境变量安全配置

## 配置步骤

### 1. 获取EdgeOne API凭证

1. 登录腾讯云控制台
2. 进入 **访问管理** > **API密钥管理**
3. 创建新的API密钥，获取：
   - `SecretId`
   - `SecretKey`

4. 进入 **EdgeOne** 控制台
5. 选择您的站点，获取：
   - `ZoneId`（站点ID）

### 2. 配置环境变量

在您的部署环境中设置以下环境变量：

```bash
# EdgeOne CDN缓存清除配置
EDGEONE_SECRET_ID="your-secret-id"
EDGEONE_SECRET_KEY="your-secret-key"
EDGEONE_ZONE_ID="your-zone-id"
EDGEONE_REGION="ap-beijing"  # 可选，默认为ap-beijing
```

### 3. 本地开发配置

复制 `.env.example` 到 `.env.local` 并填入您的配置：

```bash
cp .env.example .env.local
```

编辑 `.env.local`：

```bash
# EdgeOne CDN缓存清除配置
EDGEONE_SECRET_ID="AKIDxxxxxxxxxxxxxxxxxxxxx"
EDGEONE_SECRET_KEY="xxxxxxxxxxxxxxxxxxxxxxxx"
EDGEONE_ZONE_ID="zone-xxxxxxxxxx"
EDGEONE_REGION="ap-beijing"
```

## 使用方法

### 1. 生产环境构建（自动清除缓存）

```bash
# 构建并自动清除所有CDN缓存
pnpm run build:prod
```

### 2. 手动清除缓存

```bash
# 清除所有缓存
pnpm run clear-cache

# 清除指定URL的缓存
pnpm run clear-cache https://yourdomain.com/page1 https://yourdomain.com/page2
```

### 3. 普通构建（不清除缓存）

```bash
# 仅构建，不清除缓存
pnpm run build
```

## 脚本说明

### 可用的npm脚本

- `build:prod` - 生产环境构建 + 自动清除CDN缓存
- `clear-cache` - 手动清除CDN缓存
- `build` - 普通构建（不清除缓存）

### 缓存清除类型

1. **全站清除**：不指定URL时，清除所有缓存
2. **指定URL清除**：可以指定特定的URL进行清除

## 错误处理

### 常见错误及解决方案

1. **缺少环境变量**
   ```
   ❌ 缺少必需的环境变量:
      - EDGEONE_SECRET_ID
   ```
   **解决方案**：检查环境变量配置

2. **API权限错误**
   ```
   EdgeOne API错误: AuthFailure.SignatureFailure
   ```
   **解决方案**：检查SecretId和SecretKey是否正确

3. **Zone ID错误**
   ```
   EdgeOne API错误: InvalidParameter.ZoneNotFound
   ```
   **解决方案**：检查EDGEONE_ZONE_ID是否正确

## 部署集成

### GitHub Actions示例

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install pnpm
        run: npm install -g pnpm
        
      - name: Install dependencies
        run: pnpm install
        
      - name: Build and clear cache
        run: pnpm run build:prod
        env:
          EDGEONE_SECRET_ID: ${{ secrets.EDGEONE_SECRET_ID }}
          EDGEONE_SECRET_KEY: ${{ secrets.EDGEONE_SECRET_KEY }}
          EDGEONE_ZONE_ID: ${{ secrets.EDGEONE_ZONE_ID }}
          EDGEONE_REGION: "ap-beijing"
```

### Docker部署示例

```dockerfile
# 在Dockerfile中设置环境变量
ENV EDGEONE_SECRET_ID=""
ENV EDGEONE_SECRET_KEY=""
ENV EDGEONE_ZONE_ID=""

# 构建时清除缓存
RUN pnpm run build:prod
```

## 安全注意事项

1. **保护API密钥**：
   - 不要将API密钥提交到代码仓库
   - 使用环境变量或密钥管理服务
   - 定期轮换API密钥

2. **权限最小化**：
   - 为API密钥分配最小必要权限
   - 仅授予EdgeOne相关权限

3. **监控和日志**：
   - 监控API调用频率
   - 记录缓存清除操作日志

## 故障排除

### 调试模式

设置环境变量启用详细日志：

```bash
DEBUG=true pnpm run clear-cache
```

### 验证配置

运行以下命令验证配置是否正确：

```bash
node scripts/clear-edgeone-cache.js --dry-run
```

## 性能优化

1. **批量清除**：一次性清除多个URL比逐个清除更高效
2. **选择性清除**：仅清除实际更新的资源，而不是全站清除
3. **缓存策略**：配合适当的缓存头设置，减少清除频率

## 支持和反馈

如果遇到问题，请检查：

1. 环境变量配置是否正确
2. 网络连接是否正常
3. EdgeOne服务状态是否正常
4. API密钥权限是否足够

更多技术支持，请参考腾讯云EdgeOne官方文档。
