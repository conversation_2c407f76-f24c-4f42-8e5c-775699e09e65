import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/app/lib/auth'
import { prisma } from '@/app/lib/db'
import { log } from '@/app/lib/logger'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const { username } = await request.json()

    if (!username || !username.trim()) {
      return NextResponse.json(
        { error: '用户名不能为空' },
        { status: 400 }
      )
    }

    const trimmedUsername = username.trim()

    // 验证用户名长度
    if (trimmedUsername.length < 2 || trimmedUsername.length > 20) {
      return NextResponse.json(
        { error: '用户名长度必须在2-20个字符之间' },
        { status: 400 }
      )
    }

    // 检查用户名是否已被其他用户使用
    const existingUser = await prisma.user.findFirst({
      where: {
        username: trimmedUsername,
        id: {
          not: session.user.id
        }
      }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: '该用户名已被使用' },
        { status: 400 }
      )
    }

    // 更新用户名
    await prisma.user.update({
      where: { id: session.user.id },
      data: { username: trimmedUsername }
    })

    log.validation('用户名修改成功:', session.user.email, '新用户名:', trimmedUsername)

    return NextResponse.json(
      { message: '用户名修改成功' },
      { status: 200 }
    )
  } catch (error) {
    log.error('修改用户名错误:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
