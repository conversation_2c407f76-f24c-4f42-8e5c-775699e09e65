import type { Metada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from "next/font/google";
import AuthSessionProvider from "./components/SessionProvider";
import Navbar from "./components/Navbar";
import { NavbarProvider } from "./components/NavbarContext";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Excel学习平台 - 游戏闯关模式",
  description: "通过有趣的游戏闯关方式学习Excel，从基础操作到高级技巧",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <AuthSessionProvider>
          <NavbarProvider>
            <Navbar />
            {children}
          </NavbarProvider>
        </AuthSessionProvider>
      </body>
    </html>
  );
}
