import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/app/lib/auth'
import { prisma } from '@/app/lib/db'
import { log } from '@/app/lib/logger'

export async function DELETE() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const userId = session.user.id

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { 
        email: true, 
        username: true,
        userType: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 使用事务来确保数据一致性
    await prisma.$transaction(async (tx) => {
      // 1. 删除用户进度记录
      await tx.userProgress.deleteMany({
        where: { userId }
      })

      // 2. 如果用户拥有好友邀请码，删除这些邀请码
      await tx.friendInviteCode.deleteMany({
        where: { ownerId: userId }
      })

      // 3. 将用户使用过的邀请码的usedBy字段设为null（保留邀请码记录但清除使用者信息）
      await tx.betaInviteCode.updateMany({
        where: { usedBy: userId },
        data: { usedBy: null }
      })

      await tx.friendInviteCode.updateMany({
        where: { usedBy: userId },
        data: { usedBy: null }
      })

      // 4. 记录注销的邮箱（防止30天内重复注册）
      await tx.deletedAccount.create({
        data: {
          email: user.email
        }
      })

      // 5. 删除用户记录
      await tx.user.delete({
        where: { id: userId }
      })
    })

    log.validation('账户注销成功:', user.email, '用户名:', user.username)

    // 创建响应并清除session相关的cookies
    const response = NextResponse.json(
      { message: '账户注销成功，30天内不能使用相同邮箱重新注册' },
      { status: 200 }
    )

    // 清除NextAuth相关的cookies
    response.cookies.set('next-auth.session-token', '', {
      expires: new Date(0),
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax'
    })

    response.cookies.set('__Secure-next-auth.session-token', '', {
      expires: new Date(0),
      path: '/',
      httpOnly: true,
      secure: true,
      sameSite: 'lax'
    })

    return response
  } catch (error) {
    log.error('注销账户错误:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
