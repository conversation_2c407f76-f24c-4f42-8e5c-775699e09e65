import { redirect } from '@sveltejs/kit'
import type { PageServerLoad } from './$types'

export const load: PageServerLoad = async ({ locals, fetch }) => {
  const session = await locals.auth()
  
  // 检查是否已登录
  if (!session?.user) {
    throw redirect(302, '/auth/signin')
  }
  
  try {
    // 获取邀请码信息
    const response = await fetch('/api/invite-codes')
    
    let inviteCodes = {
      codes: [],
      total: 0,
      used: 0,
      available: 0
    }
    
    if (response.ok) {
      inviteCodes = await response.json()
    }
    
    return {
      session,
      inviteCodes
    }
  } catch (error) {
    console.error('Error loading invite codes:', error)
    
    return {
      session,
      inviteCodes: {
        codes: [],
        total: 0,
        used: 0,
        available: 0
      }
    }
  }
}
