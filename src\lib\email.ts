import nodemailer from 'nodemailer'
import { log } from './logger'

// 邮件配置
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false, // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
})

/**
 * 生成验证令牌
 */
export function generateVerificationToken(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
}

/**
 * 生成令牌过期时间（24小时后）
 */
export function generateTokenExpiry(): Date {
  const expiry = new Date()
  expiry.setHours(expiry.getHours() + 24)
  return expiry
}

/**
 * 生成密码重置过期时间（1小时后）
 */
export function generatePasswordResetExpiry(): Date {
  const expiry = new Date()
  expiry.setHours(expiry.getHours() + 1)
  return expiry
}

/**
 * 发送验证邮件
 */
export async function sendVerificationEmail(email: string, token: string): Promise<void> {
  const verificationUrl = `${process.env.PUBLIC_BASE_URL || 'http://localhost:5173'}/auth/verify-email?token=${token}`
  
  const mailOptions = {
    from: process.env.SMTP_FROM || process.env.SMTP_USER,
    to: email,
    subject: 'Excel学习平台 - 邮箱验证',
    html: `
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
        <h2 style="color: #16a34a; text-align: center;">欢迎加入Excel学习平台！</h2>
        
        <p>您好！</p>
        
        <p>感谢您注册Excel学习平台。请点击下面的链接验证您的邮箱地址：</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${verificationUrl}" 
             style="background-color: #16a34a; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
            验证邮箱
          </a>
        </div>
        
        <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
        <p style="word-break: break-all; color: #666;">${verificationUrl}</p>
        
        <p style="color: #666; font-size: 14px; margin-top: 30px;">
          此链接将在24小时后失效。如果您没有注册账户，请忽略此邮件。
        </p>
        
        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
        
        <p style="color: #999; font-size: 12px; text-align: center;">
          Excel学习平台 | 让Excel学习更简单
        </p>
      </div>
    `
  }

  try {
    await transporter.sendMail(mailOptions)
    log.info(`验证邮件已发送到: ${email}`)
  } catch (error) {
    log.error('发送验证邮件失败:', error)
    throw new Error('邮件发送失败')
  }
}

/**
 * 发送密码重置邮件
 */
export async function sendPasswordResetEmail(email: string, token: string): Promise<void> {
  const resetUrl = `${process.env.PUBLIC_BASE_URL || 'http://localhost:5173'}/auth/reset-password?token=${token}`
  
  const mailOptions = {
    from: process.env.SMTP_FROM || process.env.SMTP_USER,
    to: email,
    subject: 'Excel学习平台 - 密码重置',
    html: `
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
        <h2 style="color: #dc2626; text-align: center;">密码重置请求</h2>
        
        <p>您好！</p>
        
        <p>我们收到了您的密码重置请求。请点击下面的链接重置您的密码：</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${resetUrl}" 
             style="background-color: #dc2626; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
            重置密码
          </a>
        </div>
        
        <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
        <p style="word-break: break-all; color: #666;">${resetUrl}</p>
        
        <p style="color: #666; font-size: 14px; margin-top: 30px;">
          此链接将在1小时后失效。如果您没有请求重置密码，请忽略此邮件。
        </p>
        
        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
        
        <p style="color: #999; font-size: 12px; text-align: center;">
          Excel学习平台 | 让Excel学习更简单
        </p>
      </div>
    `
  }

  try {
    await transporter.sendMail(mailOptions)
    log.info(`密码重置邮件已发送到: ${email}`)
  } catch (error) {
    log.error('发送密码重置邮件失败:', error)
    throw new Error('邮件发送失败')
  }
}
