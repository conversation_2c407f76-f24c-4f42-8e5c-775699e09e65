import { json } from '@sveltejs/kit'
import type { RequestHand<PERSON> } from './$types'
import { prisma } from '$lib/db'
import { sendPasswordResetEmail, generateVerificationToken, generatePasswordResetExpiry } from '$lib/email'

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { email } = await request.json()

    if (!email) {
      return json(
        { error: '邮箱地址是必填的' },
        { status: 400 }
      )
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email }
    })

    if (!user) {
      // 为了安全考虑，即使用户不存在也返回成功消息
      // 这样可以防止恶意用户通过此接口探测邮箱是否存在
      return json(
        { message: '如果该邮箱地址存在于我们的系统中，您将收到密码重置邮件。有时可能会被误当作垃圾邮件放到了垃圾箱，请注意查收。' },
        { status: 200 }
      )
    }

    // 检查邮箱是否已验证
    if (!user.emailVerified) {
      return json(
        { error: '请先验证您的邮箱地址' },
        { status: 400 }
      )
    }

    // 生成密码重置令牌
    const resetToken = generateVerificationToken()
    const resetExpires = generatePasswordResetExpiry()

    // 更新用户的密码重置令牌
    await prisma.user.update({
      where: { id: user.id },
      data: {
        passwordResetToken: resetToken,
        passwordResetExpires: resetExpires
      }
    })

    // 发送密码重置邮件
    try {
      await sendPasswordResetEmail(email, resetToken)
    } catch (error) {
      console.error('密码重置邮件发送失败:', error)
      return json(
        { error: '邮件发送失败，请稍后重试' },
        { status: 500 }
      )
    }

    return json(
      // 为了安全考虑，让所有的用户都收到同样的返回成功消息
      { message: '如果该邮箱地址存在于我们的系统中，您将收到密码重置邮件。有时可能会被误当作垃圾邮件放到了垃圾箱，请注意查收。' },
      { status: 200 }
    )
  } catch (error) {
    console.error('忘记密码错误:', error)
    return json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
