/**
 * 自定义日志工具
 * 根据环境变量控制日志输出级别
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'none';

class Logger {
  private logLevel: LogLevel;
  private isDevelopment: boolean;

  constructor() {
    this.isDevelopment = process.env.NODE_ENV === 'development';
    this.logLevel = this.getLogLevel();
  }

  private getLogLevel(): LogLevel {
    // 从环境变量获取日志级别
    const envLogLevel = process.env.NEXT_PUBLIC_LOG_LEVEL as LogLevel;
    
    // 如果是开发环境，默认显示所有日志
    if (this.isDevelopment) {
      return envLogLevel || 'debug';
    }
    
    // 生产环境默认只显示错误
    return envLogLevel || 'error';
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: Record<LogLevel, number> = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3,
      none: 4
    };

    return levels[level] >= levels[this.logLevel];
  }

  debug(...args: unknown[]): void {
    if (this.shouldLog('debug')) {
      console.log('[DEBUG]', ...args);
    }
  }

  info(...args: unknown[]): void {
    if (this.shouldLog('info')) {
      console.info('[INFO]', ...args);
    }
  }

  warn(...args: unknown[]): void {
    if (this.shouldLog('warn')) {
      console.warn('[WARN]', ...args);
    }
  }

  error(...args: unknown[]): void {
    if (this.shouldLog('error')) {
      console.error('[ERROR]', ...args);
    }
  }

  // 验证相关的专用日志方法
  validation(...args: unknown[]): void {
    if (this.isDevelopment) {
      console.log('[VALIDATION]', ...args);
    }
  }

  // Univer相关的专用日志方法
  univer(...args: unknown[]): void {
    if (this.isDevelopment) {
      console.log('[UNIVER]', ...args);
    }
  }

  // 任务相关的专用日志方法
  task(...args: unknown[]): void {
    if (this.isDevelopment) {
      console.log('[TASK]', ...args);
    }
  }
}

// 创建全局日志实例
export const logger = new Logger();

// 为了向后兼容，提供简化的接口
export const log = {
  debug: logger.debug.bind(logger),
  info: logger.info.bind(logger),
  warn: logger.warn.bind(logger),
  error: logger.error.bind(logger),
  validation: logger.validation.bind(logger),
  univer: logger.univer.bind(logger),
  task: logger.task.bind(logger),
};

export default logger;
