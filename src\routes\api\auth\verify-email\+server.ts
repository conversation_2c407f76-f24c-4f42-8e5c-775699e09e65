import { json } from '@sveltejs/kit'
import type { RequestHand<PERSON> } from './$types'
import { prisma } from '$lib/db'

export const GET: RequestHandler = async ({ url }) => {
  try {
    const token = url.searchParams.get('token')

    if (!token) {
      return json(
        { error: '验证令牌缺失' },
        { status: 400 }
      )
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: {
        emailVerificationToken: token
      }
    })

    if (!user) {
      return json(
        { error: '无效的验证令牌' },
        { status: 400 }
      )
    }

    // 检查令牌是否过期
    if (user.emailVerificationExpires && user.emailVerificationExpires < new Date()) {
      return json(
        { error: '验证令牌已过期，请重新注册' },
        { status: 400 }
      )
    }

    // 检查邮箱是否已经验证
    if (user.emailVerified) {
      return json(
        { message: '邮箱已经验证过了' },
        { status: 200 }
      )
    }

    // 验证邮箱
    await prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerified: true,
        emailVerificationToken: null,
        emailVerificationExpires: null
      }
    })

    return json(
      { message: '邮箱验证成功！您现在可以登录了。' },
      { status: 200 }
    )
  } catch (error) {
    console.error('邮箱验证错误:', error)
    return json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
