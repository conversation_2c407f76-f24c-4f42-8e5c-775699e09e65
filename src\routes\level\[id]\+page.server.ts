import { redirect } from '@sveltejs/kit'
import type { PageServerLoad } from './$types'
import { prisma } from '$lib/db'

export const load: PageServerLoad = async ({ params, locals }) => {
  const session = await locals.auth()

  if (!session?.user) {
    throw redirect(302, '/auth/signin')
  }

  const levelId = params.id

  try {
    // 获取所有主任务
    const mainTasks = await prisma.level.findMany({
      where: {
        parentId: null
      },
      include: {
        children: {
          include: {
            tasks: {
              orderBy: {
                order: 'asc'
              }
            },
            progress: {
              where: {
                userId: session.user.id
              }
            }
          },
          orderBy: {
            order: 'asc'
          }
        }
      },
      orderBy: {
        order: 'asc'
      }
    })

    // 查找当前主任务
    const currentMainTask = mainTasks.find(task => task.id === levelId)

    if (!currentMainTask) {
      throw redirect(302, '/dashboard')
    }

    // 检查是否是受限关卡
    const isAdvancedLevel = currentMainTask.name === '进阶操作' || currentMainTask.name === '实用技巧'
    let isLocked = false

    if (isAdvancedLevel) {
      // 检查用户是否有邀请码或足够的经验值
      const user = await prisma.user.findUnique({
        where: { id: session.user.id }
      })

      if (user) {
        const hasInviteCode = user.userType === 'beta' || user.userType === 'invited'
        const hasEnoughScore = currentMainTask.name === '进阶操作' ? user.score >= 500 : user.score >= 600
        isLocked = !hasInviteCode && !hasEnoughScore
      }
    }

    // 设置主任务的锁定状态
    const mainTaskWithLock = {
      ...currentMainTask,
      isLocked,
      hasAccess: !isLocked,
      buttonText: isLocked ? (currentMainTask.name === '进阶操作' ? '需要500经验值' : '需要600经验值') : undefined,
      requiredScore: isLocked ? (currentMainTask.name === '进阶操作' ? 500 : 600) : undefined
    }

    return {
      session,
      mainTask: mainTaskWithLock
    }
  } catch (error) {
    console.error('获取关卡数据失败:', error)
    throw redirect(302, '/dashboard')
  }
}
