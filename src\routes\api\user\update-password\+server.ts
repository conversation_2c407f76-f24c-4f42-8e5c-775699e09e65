import { json } from '@sveltejs/kit'
import type { <PERSON>questHandler } from './$types'
import { prisma } from '$lib/db'
import bcrypt from 'bcryptjs'

export const POST: RequestHandler = async ({ request, locals }) => {
  try {
    const session = await locals.auth()
    
    if (!session?.user) {
      return json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const { currentPassword, newPassword } = await request.json()

    if (!currentPassword || !newPassword) {
      return json(
        { error: '当前密码和新密码都是必填的' },
        { status: 400 }
      )
    }

    if (newPassword.length < 6) {
      return json(
        { error: '新密码长度至少为6个字符' },
        { status: 400 }
      )
    }

    // 获取用户当前密码
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { password: true }
    })

    if (!user) {
      return json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    // 验证当前密码
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password)
    if (!isCurrentPasswordValid) {
      return json(
        { error: '当前密码不正确' },
        { status: 400 }
      )
    }

    // 加密新密码
    const hashedNewPassword = await bcrypt.hash(newPassword, 12)

    // 更新密码
    await prisma.user.update({
      where: { id: session.user.id },
      data: { password: hashedNewPassword }
    })

    return json({ success: true })
  } catch (error) {
    console.error('更新密码错误:', error)
    return json(
      { error: '服务器错误，请稍后重试' },
      { status: 500 }
    )
  }
}
