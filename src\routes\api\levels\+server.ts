import { json } from '@sveltejs/kit'
import type { Request<PERSON><PERSON><PERSON> } from './$types'
import { prisma } from '$lib/db'
import { log } from '$lib/logger'
import { getLevelAccessInfo } from '$lib/invite-codes'

export const GET: RequestHandler = async ({ locals }) => {
  try {
    const session = await locals.auth()
    
    if (!session?.user) {
      return json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    // 只获取主任务（父级关卡）
    const mainTasks = await prisma.level.findMany({
      where: {
        isMainTask: true
      },
      orderBy: {
        order: 'asc'
      },
      include: {
        children: {
          orderBy: {
            order: 'asc'
          },
          include: {
            tasks: {
              orderBy: {
                order: 'asc'
              }
            },
            progress: {
              where: {
                userId: session.user.id
              }
            }
          }
        },
        progress: {
          where: {
            userId: session.user.id
          }
        }
      }
    })

    // 获取用户信息以检查权限
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { userType: true, score: true }
    })

    // 添加权限信息到关卡数据
    const mainTasksWithPermissions = mainTasks.map(task => {
      const accessInfo = getLevelAccessInfo(task.name, user?.userType || 'normal', user?.score || 0)

      return {
        ...task,
        hasAccess: accessInfo.hasAccess,
        isLocked: accessInfo.isLocked,
        buttonText: accessInfo.buttonText,
        requiredScore: accessInfo.requiredScore
      }
    })

    return json(mainTasksWithPermissions)
  } catch (error) {
    log.error('获取关卡错误:', error)
    return json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
