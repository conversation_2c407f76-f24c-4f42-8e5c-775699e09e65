'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { log } from '@/app/lib/logger'

interface InviteCode {
  id: string
  code: string
  isUsed: boolean
  usedBy: string | null
  usedAt: string | null
  createdAt: string
}

export default function InviteCodesPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [inviteCodes, setInviteCodes] = useState<InviteCode[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')


  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  useEffect(() => {
    if (session) {
      fetchInviteCodes()
    }
  }, [session])

  const fetchInviteCodes = async () => {
    try {
      const response = await fetch('/api/invite-codes')
      if (response.ok) {
        const data = await response.json()
        setInviteCodes(data.codes || [])
      } else {
        const errorData = await response.json()
        setError(errorData.error || '获取邀请码失败')
      }
    } catch (error) {
      log.error('获取邀请码失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }



  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50" style={{ paddingTop: '2.7rem' }}>
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* 页面头部 */}
        <div className="bg-white shadow-xl rounded-2xl p-8 mb-8 border border-gray-100">
          <div className="flex items-center space-x-4 mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg">
              <span className="text-white text-2xl">🎫</span>
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">
                我的邀请码
              </h1>
              <p className="text-gray-600">
                管理您的好友邀请码，邀请朋友一起学习Excel
              </p>
            </div>
          </div>

          {/* 用户信息 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-200">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">用户类型</span>
                <span className="text-blue-600">👤</span>
              </div>
              <div className="text-xl font-bold text-blue-700">
                {session.user.userType === 'beta' ? '内测用户' : 
                 session.user.userType === 'friend' ? '好友邀请用户' : '普通用户'}
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-200">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">经验值</span>
                <span className="text-green-600">⭐</span>
              </div>
              <div className="text-xl font-bold text-green-700">
                {session.user.score}
              </div>
            </div>

            <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-4 border border-purple-200">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">邀请码数量</span>
                <span className="text-purple-600">🎯</span>
              </div>
              <div className="text-xl font-bold text-purple-700">
                {inviteCodes.length}
              </div>
            </div>
          </div>
        </div>

        {/* 错误信息 */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6">
            <div className="flex items-center space-x-2">
              <span className="text-red-500">❌</span>
              <span className="text-red-700">{error}</span>
            </div>
          </div>
        )}



        {/* 邀请码列表 */}
        <div className="bg-white shadow-xl rounded-2xl border border-gray-100 overflow-hidden">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-bold text-gray-900 flex items-center space-x-2">
              <span>🎫</span>
              <span>好友邀请码列表</span>
            </h2>
            <p className="text-gray-600 mt-2">
              分享这些邀请码给朋友，让他们也能享受完整的学习体验
            </p>
          </div>

          {inviteCodes.length === 0 ? (
            <div className="p-8 text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-gray-400 text-2xl">📭</span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无邀请码</h3>
              <p className="text-gray-600 mb-4">
                {session.user.userType === 'normal' 
                  ? '当您的经验值达到50分时，将自动获得3个好友邀请码'
                  : '您还没有获得任何好友邀请码'
                }
              </p>
              {session.user.userType === 'normal' && session.user.score < 50 && (
                <div className="bg-blue-50 rounded-lg p-4 inline-block">
                  <p className="text-blue-700 text-sm">
                    还需要 <span className="font-bold">{50 - session.user.score}</span> 经验值即可获得邀请码
                  </p>
                </div>
              )}
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {inviteCodes.map((code, index) => (
                <div key={code.id} className="p-4 sm:p-6 hover:bg-gray-50 transition-colors duration-200">
                  <div className="flex items-center justify-between gap-2">
                    <div className="flex items-center space-x-2 sm:space-x-4 min-w-0 flex-1">
                      <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-sm sm:text-base flex-shrink-0">
                        {index + 1}
                      </div>
                      <div className="min-w-0 flex-1">
                        <div>
                          <span className="text-sm sm:text-lg font-mono font-bold text-gray-900 bg-gray-100 px-2 sm:px-3 py-1 rounded-lg select-all cursor-pointer break-all">
                            {code.code}
                          </span>
                        </div>
                        <p className="text-xs sm:text-sm text-gray-500 mt-1">
                          创建时间：{new Date(code.createdAt).toLocaleString('zh-CN')}
                        </p>
                      </div>
                    </div>

                    <div className="text-right flex-shrink-0">
                      {code.isUsed ? (
                        <div>
                          <span className="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium bg-green-100 text-green-800 whitespace-nowrap">
                            ✅ 已使用
                          </span>
                          {code.usedAt && (
                            <p className="text-xs text-gray-500 mt-1 hidden sm:block">
                              使用时间：{new Date(code.usedAt).toLocaleString('zh-CN')}
                            </p>
                          )}
                        </div>
                      ) : (
                        <span className="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium bg-blue-100 text-blue-800 whitespace-nowrap">
                          🔄 未使用
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 使用说明 */}
        <div className="bg-blue-50 rounded-2xl p-6 mt-8 border border-blue-200">
          <h3 className="text-lg font-semibold text-blue-900 mb-3 flex items-center space-x-2">
            <span>💡</span>
            <span>使用说明</span>
          </h3>
          <div className="space-y-2 text-blue-800">
            <p>• 每个邀请码只能使用一次</p>
            <p>• 朋友使用您的邀请码注册后，可以解锁&ldquo;进阶操作&rdquo;和&ldquo;实用技巧&rdquo;关卡</p>
            <p>• 内测用户注册时自动获得5个好友邀请码</p>
            <p>• 普通用户达到50经验值时自动获得3个好友邀请码</p>
            <p>• 点击邀请码可选中文本，然后使用 Ctrl+C (或 Cmd+C) 复制</p>
          </div>
        </div>
      </div>
    </div>
  )
}
