'use client';

import { useEffect } from 'react';

/**
 * UniverSheet预加载组件
 * 在用户可能需要使用Excel功能之前预加载关键资源
 */
export default function UniverPreloader() {
  useEffect(() => {
    // 只在浏览器环境中执行
    if (typeof window === 'undefined') return;

    // 检查是否支持预加载
    const supportsPreload = 'link' in document.createElement('link');
    if (!supportsPreload) return;

    // 预加载关键的Univer资源
    const preloadResources = [
      // 核心CSS文件
      '/_next/static/css/univer-core.css',
      // 核心JS文件
      '/_next/static/chunks/univer-core.js',
    ];

    const preloadedLinks: HTMLLinkElement[] = [];

    preloadResources.forEach(href => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = href.endsWith('.css') ? 'style' : 'script';
      link.href = href;
      link.crossOrigin = 'anonymous';
      
      // 添加到head
      document.head.appendChild(link);
      preloadedLinks.push(link);
    });

    // 预加载字体（如果Univer使用特定字体）
    const fontPreload = document.createElement('link');
    fontPreload.rel = 'preload';
    fontPreload.as = 'font';
    fontPreload.type = 'font/woff2';
    fontPreload.crossOrigin = 'anonymous';
    // fontPreload.href = '/fonts/univer-icons.woff2'; // 如果有的话

    // 使用Intersection Observer预测用户行为
    const observeExcelTriggers = () => {
      const excelTriggers = document.querySelectorAll('[data-excel-trigger]');
      
      if (excelTriggers.length === 0) return;

      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            // 用户接近Excel相关元素，开始预加载
            preloadUniverModules();
            observer.disconnect(); // 只预加载一次
          }
        });
      }, {
        rootMargin: '100px' // 提前100px开始预加载
      });

      excelTriggers.forEach(trigger => observer.observe(trigger));
    };

    // 预加载Univer模块
    const preloadUniverModules = () => {
      // 预加载懒加载的模块
      import('./lazy').catch(() => {
        // 静默失败，不影响用户体验
      });

      // 预加载基础功能模块
      Promise.all([
        import('@univerjs/sheets-formula'),
        import('@univerjs/sheets-numfmt'),
        import('@univerjs/sheets-data-validation'),
      ]).catch(() => {
        // 静默失败
      });
    };

    // 延迟执行观察器设置，避免阻塞初始渲染
    setTimeout(observeExcelTriggers, 1000);

    // 清理函数
    return () => {
      preloadedLinks.forEach(link => {
        if (link.parentNode) {
          link.parentNode.removeChild(link);
        }
      });
    };
  }, []);

  // 这个组件不渲染任何内容
  return null;
}

/**
 * 智能预加载Hook
 * 根据用户行为模式预加载资源
 */
export function useSmartPreload() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    let idleCallbackId: number;
    let timeoutId: NodeJS.Timeout;

    // 在浏览器空闲时预加载
    const preloadOnIdle = () => {
      if ('requestIdleCallback' in window) {
        idleCallbackId = window.requestIdleCallback(() => {
          // 预加载Univer基础模块
          import('./lazy').catch(() => {});
        }, { timeout: 5000 });
      } else {
        timeoutId = setTimeout(() => {
          import('./lazy').catch(() => {});
        }, 2000);
      }
    };

    // 监听用户交互
    const handleUserInteraction = () => {
      // 用户开始交互，可能很快需要Excel功能
      preloadOnIdle();
      
      // 移除监听器，只预加载一次
      window.removeEventListener('click', handleUserInteraction);
      window.removeEventListener('scroll', handleUserInteraction);
      window.removeEventListener('keydown', handleUserInteraction);
    };

    // 添加交互监听器
    window.addEventListener('click', handleUserInteraction, { passive: true });
    window.addEventListener('scroll', handleUserInteraction, { passive: true });
    window.addEventListener('keydown', handleUserInteraction, { passive: true });

    return () => {
      if (idleCallbackId) {
        window.cancelIdleCallback(idleCallbackId);
      }
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      window.removeEventListener('click', handleUserInteraction);
      window.removeEventListener('scroll', handleUserInteraction);
      window.removeEventListener('keydown', handleUserInteraction);
    };
  }, []);
}

/**
 * 资源预加载工具函数
 */
export const preloadResource = (href: string, as: string = 'script') => {
  if (typeof document === 'undefined') return;

  const link = document.createElement('link');
  link.rel = 'preload';
  link.as = as;
  link.href = href;
  link.crossOrigin = 'anonymous';
  
  document.head.appendChild(link);
  
  return () => {
    if (link.parentNode) {
      link.parentNode.removeChild(link);
    }
  };
};

/**
 * 模块预加载工具
 */
export const preloadModules = async (modules: string[]) => {
  const promises = modules.map(module => 
    import(module).catch(() => null) // 静默失败
  );
  
  return Promise.allSettled(promises);
};
