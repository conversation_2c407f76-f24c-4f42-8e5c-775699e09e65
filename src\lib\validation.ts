import type { UniverAPI, ValidationRule, ValidationResult } from './types/univer'
import { log } from './logger'

/**
 * 验证任务 - 简化的函数式接口
 */
export async function validateTask(univerAPI: UniverAPI, validationRule: ValidationRule): Promise<ValidationResult> {
  try {
    log.debug('开始验证任务:', validationRule)

    const service = new ExcelValidationService(univerAPI)

    switch (validationRule.type) {
      case 'cellValue':
      case 'input':
        return await service.validateCellValue(validationRule)
      case 'cellStyle':
        return await service.validateCellStyle(validationRule)
      case 'cellFormula':
        return await service.validateCellFormula(validationRule)
      case 'cellFormat':
        return await service.validateCellFormat(validationRule)
      case 'chart':
        return await service.validateChart(validationRule)
      case 'pivotTable':
        return await service.validatePivotTable(validationRule)
      case 'filter':
        return await service.validateFilter(validationRule)
      case 'sort':
        return await service.validateSort(validationRule)
      case 'multiSort':
        return await service.validateMultiSort(validationRule)
      case 'conditional_format':
      case 'conditionalFormat':
      case 'multiConditionalFormat':
        return await service.validateConditionalFormat(validationRule)
      case 'multiCellAlignment':
        return await service.validateMultiCellAlignment(validationRule)
      case 'multiBorder':
        return await service.validateMultiBorder(validationRule)
      case 'dataValidation':
        return await service.validateDataValidation(validationRule)
      case 'cellMerge':
        return await service.validateCellMerge(validationRule)
      case 'textWrap':
        return await service.validateTextWrap(validationRule)
      case 'formulaFill':
        return await service.validateFormulaFill(validationRule)
      default:
        return {
          success: false,
          message: `不支持的验证类型: ${validationRule.type}`
        }
    }
  } catch (error) {
    log.error('验证任务时发生错误:', error)
    return {
      success: false,
      message: '验证过程中发生错误，请重试'
    }
  }
}

/**
 * Excel验证服务 - 基于官方Univer API
 */
export class ExcelValidationService {
  constructor(private univerAPI: UniverAPI) {}

  /**
   * 提取 Univer 单元格的实际值
   */
  private extractCellValue(cellValue: unknown): unknown {
    if (cellValue && typeof cellValue === 'object' && 'v' in cellValue) {
      return (cellValue as { v: unknown }).v;
    }
    return cellValue;
  }

  /**
   * 比较两个值是否相等
   */
  private compareValues(actual: unknown, expected: unknown): boolean {
    // 处理数字比较
    if (typeof expected === 'number' && typeof actual === 'number') {
      return Math.abs(actual - expected) < 0.0001 // 浮点数比较
    }

    // 处理字符串比较（忽略大小写和前后空格）
    if (typeof expected === 'string' && typeof actual === 'string') {
      return actual.trim().toLowerCase() === expected.trim().toLowerCase()
    }

    // 其他类型直接比较
    return actual === expected
  }

  /**
   * 验证单元格值
   */
  async validateCellValue(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell || rule.expectedValue === undefined) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置或期望值'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook();
      if (!workbook) throw new Error('未获取到工作簿');
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取活动工作表'
        }
      }

      const range = worksheet.getRange(rule.cell)
      if (!range) {
        return {
          success: false,
          message: `无法获取单元格 ${rule.cell}`
        }
      }

      const cellValue = range.getValue()
      const actualValue = this.extractCellValue(cellValue)

      // 类型转换和比较
      const expectedValue = rule.expectedValue
      const isMatch = this.compareValues(actualValue, expectedValue)

      return {
        success: isMatch,
        message: isMatch
          ? '单元格值验证通过！'
          : `单元格 ${rule.cell} 的值不正确。期望: "${expectedValue}"，实际: "${actualValue}"`,
        details: {
          cell: rule.cell,
          expected: expectedValue,
          actual: actualValue
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `验证单元格值时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证单元格样式
   */
  async validateCellStyle(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell || !rule.expectedStyle) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置或期望样式'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook();
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }
      const range = worksheet.getRange(rule.cell)

      // 获取单元格数据和样式信息
      const cellData = range.getCellData()
      let style: any = {}

      // 根据Univer文档，样式可能是ID引用或直接的样式对象
      if (cellData?.s) {
        if (typeof cellData.s === 'string') {
          // 如果是字符串，说明是样式ID，需要从工作簿样式表中获取
          const workbook = this.univerAPI.getActiveWorkbook()
          let styles = {}

          try {
            // 使用save()方法获取工作簿数据，按照文档建议
            try {
              const workbookData = await (workbook as unknown as { save(): Promise<{ styles?: Record<string, any> }> }).save()
              styles = workbookData?.styles || {}
              log.debug('使用save()获取样式表:', { totalStyles: Object.keys(styles).length })
            } catch (saveError) {
              log.debug('save()方法失败，尝试其他方法:', saveError)

              // 备用方法：尝试从Univer实例获取
              const univerInstance = (this.univerAPI as unknown as { _univerInstance?: unknown })?._univerInstance
              if (univerInstance) {
                const currentWorkbook = (univerInstance as { getCurrentUniverSheetInstance(): unknown }).getCurrentUniverSheetInstance()
                const workbookSnapshot = (currentWorkbook as { save?(): unknown; getSnapshot?(): unknown })?.save?.() || (currentWorkbook as { save?(): unknown; getSnapshot?(): unknown })?.getSnapshot?.()
                styles = (workbookSnapshot as { styles?: Record<string, any> })?.styles || {}
              }
            }

            log.debug('获取到的样式表:', { totalStyles: Object.keys(styles).length, styleId: cellData.s })
          } catch (error) {
            log.debug('获取样式表失败:', error)
          }

          style = styles[cellData.s] || {}
          log.debug('从样式表获取样式:', { styleId: cellData.s, style, hasStyle: !!styles[cellData.s] })
        } else {
          // 如果是对象，直接使用
          style = cellData.s
          log.debug('直接使用样式对象:', style)
        }
      }

      log.debug('单元格数据和样式:', { cellData, style, styleType: typeof cellData?.s })

      const validationResults = []

      // 验证粗体
      if (rule.expectedStyle.bold !== undefined) {
        // 只检查标准的粗体属性，避免过于宽松的验证
        const isBold = style.bl === 1 || style.bl === true ||
                      style.bold === 1 || style.bold === true ||
                      style.fontWeight === 'bold' || style.fontWeight === 700 ||
                      (style.ft && (style.ft.bl === 1 || style.ft.bl === true))
        const expectedBold = rule.expectedStyle.bold

        log.debug('粗体验证调试信息:', {
          cell: rule.cell,
          style: style,
          isBold: isBold,
          expectedBold: expectedBold,
          styleKeys: Object.keys(style),
          styleType: typeof style
        })

        validationResults.push({
          property: 'bold',
          expected: expectedBold,
          actual: isBold,
          match: isBold === expectedBold
        })
      }

      // 验证斜体
      if (rule.expectedStyle.italic !== undefined) {
        // 支持多种斜体属性格式
        const isItalic = style.it === 1 || style.it === true ||
                         style.italic === 1 || style.italic === true ||
                         style.fontStyle === 'italic' ||
                         (style.ft && (style.ft.it === 1 || style.ft.it === true))
        const expectedItalic = rule.expectedStyle.italic

        log.debug('斜体验证调试信息:', {
          cell: rule.cell,
          style: style,
          isItalic: isItalic,
          expectedItalic: expectedItalic,
          styleKeys: Object.keys(style)
        })

        validationResults.push({
          property: 'italic',
          expected: expectedItalic,
          actual: isItalic,
          match: isItalic === expectedItalic
        })
      }

      // 验证字体系列
      if (rule.expectedStyle.fontFamily) {
        // 从样式对象获取字体系列 (ff属性)
        const actualFontFamily = style.ff || ''
        const expectedFontFamily = rule.expectedStyle.fontFamily

        log.debug('字体系列获取:', { actualFontFamily, expectedFontFamily, style })

        // 字体名称标准化
        const normalizeFont = (font: string) => font.toLowerCase().replace(/['"]/g, '').trim()
        const isMatch = normalizeFont(actualFontFamily) === normalizeFont(expectedFontFamily)

        validationResults.push({
          property: 'fontFamily',
          expected: expectedFontFamily,
          actual: actualFontFamily,
          match: isMatch
        })
      }

      // 检查所有验证结果
      const allMatch = validationResults.every(result => result.match)
      const failedResults = validationResults.filter(result => !result.match)

      if (allMatch) {
        return {
          success: true,
          message: '样式验证通过！',
          details: { validationResults }
        }
      } else {
        const failureMessages = failedResults.map(result => 
          `${result.property}: 期望 ${result.expected}, 实际 ${result.actual}`
        ).join('; ')
        
        return {
          success: false,
          message: `单元格 ${rule.cell} 的样式不正确。${failureMessages}`,
          details: { validationResults, failedResults }
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `验证样式时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证单元格公式
   */
  async validateCellFormula(rule: ValidationRule): Promise<ValidationResult> {
    return {
      success: false,
      message: '公式验证功能正在开发中'
    }
  }

  /**
   * 验证单元格格式
   */
  async validateCellFormat(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      if (!rule.cell) {
        return {
          success: false,
          message: '缺少单元格位置信息'
        }
      }

      const range = worksheet.getRange(rule.cell)
      const format = range.getNumberFormat()

      log.debug(`单元格 ${rule.cell} 的格式:`, format)

      if (rule.expectedFormat && format !== rule.expectedFormat) {
        return {
          success: false,
          message: `单元格 ${rule.cell} 的格式不正确。期望: ${rule.expectedFormat}, 实际: ${format}`
        }
      }

      return {
        success: true,
        message: '单元格格式验证通过'
      }
    } catch (error) {
      log.error('验证单元格格式时发生错误:', error)
      return {
        success: false,
        message: '验证单元格格式时发生错误'
      }
    }
  }

  /**
   * 验证图表
   */
  async validateChart(rule: ValidationRule): Promise<ValidationResult> {
    try {
      // 检查DOM中是否存在图表相关元素
      const chartSelectors = [
        '.univer-chart',
        '.echarts-chart',
        '.chart-container',
        '[data-chart-id]',
        'canvas[data-zr-dom-id]', // ECharts canvas
        '.univer-drawing-object' // Univer绘图对象
      ]

      let chartFound = false
      for (const selector of chartSelectors) {
        const elements = document.querySelectorAll(selector)
        if (elements.length > 0) {
          chartFound = true
          log.debug(`找到图表元素: ${selector}, 数量: ${elements.length}`)
          break
        }
      }

      if (!chartFound) {
        return {
          success: false,
          message: '未找到图表。请按照操作步骤创建图表：\n1. 选择数据范围\n2. 点击"插入"选项卡\n3. 选择"图表"\n4. 选择合适的图表类型'
        }
      }

      return {
        success: true,
        message: '图表创建成功！任务完成。'
      }

    } catch (error) {
      log.error('图表验证错误:', error)
      return {
        success: false,
        message: `图表验证时发生错误: ${error}`
      }
    }
  }

  /**
   * 验证数据透视表
   */
  async validatePivotTable(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      // 检查DOM中是否存在透视表相关元素
      const pivotSelectors = [
        '.univer-pivot-table',
        '.pivot-table',
        '[data-pivot-id]',
        '.univer-pivot'
      ]

      let pivotFound = false
      for (const selector of pivotSelectors) {
        const elements = document.querySelectorAll(selector)
        if (elements.length > 0) {
          pivotFound = true
          log.debug(`找到透视表元素: ${selector}, 数量: ${elements.length}`)
          break
        }
      }

      // 检查当前工作表中是否有透视表的特征数据结构
      let hasPivotStructure = false
      try {
        const range = worksheet.getRange('A1:Z50')
        const values = range.getValues()

        if (values && values.length > 0) {
          for (let i = 0; i < Math.min(values.length, 20); i++) {
            for (let j = 0; j < Math.min(values[i].length, 20); j++) {
              const cellValue = values[i][j]
              if (cellValue && typeof cellValue === 'string') {
                const pivotKeywords = ['总计', '小计', '求和', '计数', '平均值', '最大值', '最小值', 'Sum', 'Count', 'Average', 'Total']
                if (pivotKeywords.some(keyword => cellValue.includes(keyword))) {
                  hasPivotStructure = true
                  log.debug(`在单元格找到透视表特征: ${cellValue}`)
                  break
                }
              }
            }
            if (hasPivotStructure) break
          }
        }
      } catch (e) {
        log.debug('检查透视表结构失败:', e)
      }

      const pivotCreated = pivotFound || hasPivotStructure

      if (!pivotCreated) {
        return {
          success: false,
          message: '未检测到数据透视表。请按照操作步骤创建透视表：\n1. 选择数据范围\n2. 右键点击选择"数据透视表"或通过"插入"菜单\n3. 确认数据范围并选择放置位置\n4. 点击"确定"创建透视表'
        }
      }

      return {
        success: true,
        message: '数据透视表创建成功！任务完成。'
      }

    } catch (error) {
      log.error('透视表验证错误:', error)
      return {
        success: false,
        message: `透视表验证时发生错误: ${error}`
      }
    }
  }

  /**
   * 验证筛选
   */
  async validateFilter(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      // 使用官方FFilter API进行验证
      const filter = worksheet.getFilter()
      log.debug('筛选验证 - 获取筛选器:', filter)

      if (!filter) {
        return {
          success: false,
          message: '未检测到筛选器。请确保已正确执行以下步骤：\n\n1. 选择数据范围（包含表头）\n2. 启用筛选功能（数据 → 筛选）\n3. 设置筛选条件\n\n提示：筛选功能会在表头显示下拉箭头。'
        }
      }

      return {
        success: true,
        message: '筛选功能验证成功！任务完成。'
      }

    } catch (error) {
      log.error('筛选验证错误:', error)
      return {
        success: false,
        message: `筛选验证时发生错误: ${error}`
      }
    }
  }

  /**
   * 验证排序
   */
  async validateSort(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      // 简化的排序验证 - 检查数据是否已排序
      if (rule.range) {
        const range = worksheet.getRange(rule.range)
        const values = range.getValues()

        if (values && values.length > 1) {
          // 检查第一列是否已排序
          const firstColumn = values.map(row => row[0]).filter(val => val !== null && val !== undefined)

          if (firstColumn.length > 1) {
            const isAscending = firstColumn.every((val, i) => i === 0 || val >= firstColumn[i - 1])
            const isDescending = firstColumn.every((val, i) => i === 0 || val <= firstColumn[i - 1])

            if (isAscending || isDescending) {
              return {
                success: true,
                message: '排序验证成功！数据已正确排序。'
              }
            }
          }
        }
      }

      return {
        success: false,
        message: '未检测到排序。请确保已正确执行以下步骤：\n1. 选择数据范围\n2. 点击"数据"菜单中的"排序"\n3. 选择排序列和排序方向\n4. 点击"确定"'
      }

    } catch (error) {
      log.error('排序验证错误:', error)
      return {
        success: false,
        message: `排序验证时发生错误: ${error}`
      }
    }
  }

  /**
   * 验证多列排序
   */
  async validateMultiSort(rule: ValidationRule): Promise<ValidationResult> {
    // 简化实现，与单列排序类似
    return await this.validateSort(rule)
  }

  /**
   * 验证条件格式
   */
  async validateConditionalFormat(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取工作簿，请确保Excel组件已正确加载'
        }
      }
      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取工作表，请确保Excel组件已正确加载'
        }
      }

      // 简化的条件格式验证
      if (rule.range) {
        const range = worksheet.getRange(rule.range)

        try {
          const conditionalRules = range.getConditionalFormattingRules()
          log.debug('获取到的条件格式规则:', conditionalRules)

          if (!conditionalRules || conditionalRules.length === 0) {
            return {
              success: false,
              message: `请设置条件格式。\n\n操作步骤：\n1. 选择数据范围 ${rule.range}\n2. 点击"数据"菜单中的"条件格式"\n3. 选择"突出显示单元格规则"\n4. 设置条件和格式\n5. 点击"确定"`
            }
          }

          return {
            success: true,
            message: '条件格式验证成功！已正确设置条件格式规则。'
          }
        } catch (e) {
          log.debug('获取条件格式规则失败:', e)
          return {
            success: false,
            message: '无法验证条件格式，请确保已正确设置条件格式。'
          }
        }
      }

      return {
        success: false,
        message: '缺少验证范围信息'
      }

    } catch (error) {
      log.error('条件格式验证错误:', error)
      return {
        success: false,
        message: `条件格式验证时发生错误: ${error}`
      }
    }
  }

  /**
   * 验证多单元格对齐
   */
  async validateMultiCellAlignment(rule: ValidationRule): Promise<ValidationResult> {
    return {
      success: false,
      message: '多单元格对齐验证功能正在开发中'
    }
  }

  /**
   * 验证多边框
   */
  async validateMultiBorder(rule: ValidationRule): Promise<ValidationResult> {
    return {
      success: false,
      message: '多边框验证功能正在开发中'
    }
  }

  /**
   * 验证数据验证
   */
  async validateDataValidation(rule: ValidationRule): Promise<ValidationResult> {
    return {
      success: false,
      message: '数据验证功能正在开发中'
    }
  }

  /**
   * 验证单元格合并
   */
  async validateCellMerge(rule: ValidationRule): Promise<ValidationResult> {
    return {
      success: false,
      message: '单元格合并验证功能正在开发中'
    }
  }

  /**
   * 验证文本换行
   */
  async validateTextWrap(rule: ValidationRule): Promise<ValidationResult> {
    return {
      success: false,
      message: '文本换行验证功能正在开发中'
    }
  }

  /**
   * 验证公式填充
   */
  async validateFormulaFill(rule: ValidationRule): Promise<ValidationResult> {
    return {
      success: false,
      message: '公式填充验证功能正在开发中'
    }
  }
}
