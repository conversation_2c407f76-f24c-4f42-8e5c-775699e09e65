import nodemailer from 'nodemailer'

import { log } from '@/app/lib/logger';
// 创建邮件传输器
const createTransporter = () => {
  return nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: parseInt(process.env.EMAIL_PORT || '587'),
    secure: true, // true for 465, false for other ports
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  })
}

// 发送邮箱验证邮件
export async function sendVerificationEmail(email: string, token: string) {
  const transporter = createTransporter()

  const verificationUrl = `${process.env.NEXTAUTH_URL}/auth/verify-email?token=${token}`
  
  const mailOptions = {
    from: process.env.EMAIL_FROM,
    to: email,
    subject: 'Excel学习平台 - 邮箱验证',
    html: `
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #16a34a; margin: 0;">Excel学习平台</h1>
        </div>
        
        <div style="background-color: #f9f9f9; padding: 30px; border-radius: 8px;">
          <h2 style="color: #333; margin-top: 0;">验证您的邮箱地址</h2>
          
          <p style="color: #666; line-height: 1.6;">
            感谢您注册Excel学习平台！为了完成注册，请点击下面的按钮验证您的邮箱地址：
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationUrl}" 
               style="background-color: #16a34a; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
              验证邮箱
            </a>
          </div>
          
          <p style="color: #666; line-height: 1.6; font-size: 14px;">
            如果按钮无法点击或者打开的页面无法正常工作，请复制以下链接到浏览器地址栏：<br>
            <a href="${verificationUrl}" style="color: #16a34a; word-break: break-all;">${verificationUrl}</a>
          </p>
          
          <p style="color: #999; font-size: 12px; margin-top: 30px;">
            此验证链接将在24小时后过期。如果您没有注册Excel学习平台，请忽略此邮件。
          </p>
        </div>
        
        <div style="text-align: center; margin-top: 20px; color: #999; font-size: 12px;">
          <p>© 2025 Excel学习平台. 保留所有权利。</p>
        </div>
      </div>
    `,
  }

  try {
    await transporter.sendMail(mailOptions)
    log.validation('验证邮件发送成功:', email)
  } catch (error) {
    log.error('邮件发送失败:', error)
    throw new Error('邮件发送失败')
  }
}

// 生成验证令牌
export function generateVerificationToken(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15) +
         Date.now().toString(36)
}

// 发送密码重置邮件
export async function sendPasswordResetEmail(email: string, token: string) {
  const transporter = createTransporter()

  const resetUrl = `${process.env.NEXTAUTH_URL}/auth/reset-password?token=${token}`

  const mailOptions = {
    from: process.env.EMAIL_FROM,
    to: email,
    subject: 'Excel学习平台 - 密码重置',
    html: `
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #16a34a; margin: 0;">Excel学习平台</h1>
        </div>

        <div style="background-color: #f9f9f9; padding: 30px; border-radius: 8px;">
          <h2 style="color: #333; margin-top: 0;">重置您的密码</h2>

          <p style="color: #666; line-height: 1.6;">
            您请求重置Excel学习平台的密码。请点击下面的按钮来设置新密码：
          </p>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}"
               style="background-color: #16a34a; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
              重置密码
            </a>
          </div>

          <p style="color: #666; line-height: 1.6; font-size: 14px;">
            如果按钮无法点击或者打开的页面无法正常工作，请复制以下链接到浏览器地址栏：<br>
            <a href="${resetUrl}" style="color: #16a34a; word-break: break-all;">${resetUrl}</a>
          </p>

          <p style="color: #999; font-size: 12px; margin-top: 30px;">
            此重置链接将在1小时后过期。如果您没有请求重置密码，请忽略此邮件。
          </p>
        </div>

        <div style="text-align: center; margin-top: 20px; color: #999; font-size: 12px;">
          <p>© 2025 Excel学习平台. 保留所有权利。</p>
        </div>
      </div>
    `,
  }

  try {
    await transporter.sendMail(mailOptions)
    log.validation('密码重置邮件发送成功:', email)
  } catch (error) {
    log.error('密码重置邮件发送失败:', error)
    throw new Error('邮件发送失败')
  }
}

// 生成令牌过期时间（24小时后）
export function generateTokenExpiry(): Date {
  return new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时
}

// 生成密码重置令牌过期时间（1小时后）
export function generatePasswordResetExpiry(): Date {
  return new Date(Date.now() + 60 * 60 * 1000) // 1小时
}
