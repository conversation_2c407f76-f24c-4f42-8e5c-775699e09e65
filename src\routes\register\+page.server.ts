import { fail, redirect } from '@sveltejs/kit'
import type { Actions, PageServerLoad } from './$types'

export const load: PageServerLoad = async ({ locals }) => {
  const session = await locals.auth()
  
  // 如果已经登录，重定向到仪表板
  if (session?.user) {
    throw redirect(302, '/dashboard')
  }
  
  return {}
}

export const actions: Actions = {
  default: async ({ request, fetch }) => {
    const data = await request.formData()
    const email = data.get('email') as string
    const username = data.get('username') as string
    const password = data.get('password') as string
    const confirmPassword = data.get('confirmPassword') as string
    const inviteCode = data.get('inviteCode') as string
    const agreeToTerms = data.get('agreeToTerms') as string

    // 验证输入
    if (!email || !username || !password || !confirmPassword) {
      return fail(400, {
        error: '请填写所有必填字段'
      })
    }

    if (password !== confirmPassword) {
      return fail(400, {
        error: '两次输入的密码不一致'
      })
    }

    if (password.length < 6) {
      return fail(400, {
        error: '密码长度至少为6位'
      })
    }

    if (!agreeToTerms) {
      return fail(400, {
        error: '请先阅读并同意用户服务协议和隐私政策'
      })
    }

    try {
      // 调用注册 API
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email,
          username,
          password,
          inviteCode: inviteCode || undefined,
          agreeToTerms: true
        })
      })

      const result = await response.json()

      if (!response.ok) {
        return fail(response.status, {
          error: result.error || '注册失败，请重试'
        })
      }

      return {
        success: true,
        message: result.message
      }
    } catch (error) {
      console.error('注册错误:', error)
      return fail(500, {
        error: '服务器错误，请稍后重试'
      })
    }
  }
}
