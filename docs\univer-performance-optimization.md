# UniverSheet 性能优化指南

## 🎯 优化目标

将UniverSheet首次加载时间从10+秒缩短到3-5秒，提升用户体验。

## 📊 当前优化措施

### 1. 已实现的优化

#### ✅ 插件懒加载
- **核心插件**: 立即加载（渲染引擎、公式引擎、基础UI）
- **基础功能**: 使用`requestIdleCallback`优化加载时机
- **高级功能**: 延迟500ms加载（图表、透视表）

#### ✅ 代码分割优化
- **React包**: 独立chunk，优先级40
- **Univer核心**: 独立chunk，优先级30，强制分割
- **Univer基础**: 异步chunk，优先级20
- **Univer高级**: 异步chunk，优先级10
- **第三方库**: 通用vendor chunk

#### ✅ 性能监控
- 集成性能监控工具
- 实时监控加载时间
- 资源加载分析
- 长任务检测

#### ✅ 智能预加载
- 基于用户行为预测
- 空闲时间预加载
- 交互触发预加载

## 🚀 新增优化功能

### 1. Bundle分析工具

```bash
# 分析bundle大小和组成
pnpm analyze

# 生成详细的bundle分析报告
pnpm build:analyze
```

### 2. 性能监控

```typescript
import { startTimer, endTimer, getPerformanceReport } from '@/app/lib/performance';

// 监控特定操作
startTimer('operation-name');
// ... 执行操作
endTimer('operation-name');

// 获取性能报告
console.log(getPerformanceReport());
```

### 3. 智能预加载

```typescript
import { useSmartPreload, preloadModules } from '@/app/components/UniverPreloader';

// 在组件中使用智能预加载
function MyComponent() {
  useSmartPreload();
  return <div>...</div>;
}

// 手动预加载模块
preloadModules(['@univerjs/sheets-formula', '@univerjs/sheets-filter']);
```

## 📈 性能提升预期

| 优化项目 | 预期提升 | 实现方式 |
|---------|---------|----------|
| 首次可交互时间 | 40-60% | 核心插件优先加载 |
| 基础功能可用 | 30-50% | requestIdleCallback优化 |
| 资源加载效率 | 20-30% | 智能代码分割 |
| 缓存命中率 | 50-70% | 细粒度chunk分割 |

## 🔧 使用指南

### 1. 开发环境监控

```bash
# 启动开发服务器（自动启用性能监控）
pnpm dev
```

开发环境会自动显示：
- ⏱️ 各阶段加载时间
- 📦 资源加载详情
- 🐌 长任务警告
- 💾 内存使用情况

### 2. 生产环境分析

```bash
# 构建并分析
pnpm build
pnpm analyze
```

### 3. 性能基准测试

```bash
# 运行性能测试
pnpm test:performance
```

## 🎛️ 配置选项

### 1. 懒加载时机调整

在`UniverSheet.tsx`中调整加载时机：

```typescript
// 基础功能加载时机（当前：requestIdleCallback）
if ('requestIdleCallback' in window) {
  window.requestIdleCallback(loadBasicPlugins, { timeout: 100 });
} else {
  setTimeout(loadBasicPlugins, 16);
}

// 高级功能加载时机（当前：500ms）
setTimeout(loadAdvancedPlugins, 500);
```

### 2. 代码分割配置

在`next.config.ts`中调整分割策略：

```typescript
splitChunks: {
  chunks: 'all',
  minSize: 20000,      // 最小chunk大小
  maxSize: 244000,     // 最大chunk大小
  // ... 其他配置
}
```

## 📋 最佳实践

### 1. 开发时
- 使用性能监控工具识别瓶颈
- 定期运行bundle分析
- 关注控制台性能警告

### 2. 部署前
- 运行完整的性能测试
- 检查bundle大小是否合理
- 验证懒加载是否正常工作

### 3. 生产环境
- 启用gzip/brotli压缩
- 配置适当的缓存策略
- 监控实际用户性能指标

## 🔍 故障排除

### 1. 加载时间仍然很长
- 检查网络条件
- 运行`pnpm analyze`查看bundle大小
- 检查是否有重复依赖

### 2. 功能加载失败
- 查看浏览器控制台错误
- 检查懒加载模块是否正确导入
- 验证插件注册顺序

### 3. 性能监控不工作
- 确认在开发环境中运行
- 检查浏览器是否支持Performance API
- 查看控制台是否有错误信息

## 📊 监控指标

### 关键性能指标 (KPI)
- **首次内容绘制 (FCP)**: < 1.5秒
- **最大内容绘制 (LCP)**: < 2.5秒
- **首次输入延迟 (FID)**: < 100毫秒
- **累积布局偏移 (CLS)**: < 0.1

### Univer特定指标
- **核心插件加载时间**: < 500毫秒
- **基础功能可用时间**: < 2秒
- **全功能可用时间**: < 5秒

## 🔮 未来优化方向

1. **Service Worker缓存**: 实现更智能的缓存策略
2. **HTTP/2推送**: 预推送关键资源
3. **WebAssembly**: 将计算密集型功能迁移到WASM
4. **CDN优化**: 将Univer资源部署到CDN
5. **按需加载**: 根据用户实际使用的功能动态加载

## 📞 支持

如果遇到性能问题或需要进一步优化建议，请：
1. 运行`pnpm analyze`收集数据
2. 查看浏览器开发者工具的Performance面板
3. 提供具体的性能指标和错误信息
