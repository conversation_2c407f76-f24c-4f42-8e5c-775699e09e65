import { prisma } from './db'

/**
 * 生成随机邀请码
 * @param length 邀请码长度
 * @returns 随机邀请码字符串
 */
function generateRandomCode(length: number): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 生成唯一的内测邀请码（8位）
 * @returns 唯一的内测邀请码
 */
export async function generateUniqueBetaCode(): Promise<string> {
  let code: string
  let isUnique = false
  
  while (!isUnique) {
    code = generateRandomCode(8)
    
    // 检查内测邀请码表中是否已存在
    const existingBeta = await prisma.betaInviteCode.findUnique({
      where: { code }
    })
    
    // 检查好友邀请码表中是否已存在（确保全系统唯一）
    const existingFriend = await prisma.friendInviteCode.findUnique({
      where: { code }
    })
    
    if (!existingBeta && !existingFriend) {
      isUnique = true
    }
  }
  
  return code!
}

/**
 * 生成唯一的好友邀请码（6位）
 * @returns 唯一的好友邀请码
 */
export async function generateUniqueFriendCode(): Promise<string> {
  let code: string
  let isUnique = false
  
  while (!isUnique) {
    code = generateRandomCode(6)
    
    // 检查好友邀请码表中是否已存在
    const existingFriend = await prisma.friendInviteCode.findUnique({
      where: { code }
    })
    
    // 检查内测邀请码表中是否已存在（确保全系统唯一）
    const existingBeta = await prisma.betaInviteCode.findUnique({
      where: { code }
    })
    
    if (!existingFriend && !existingBeta) {
      isUnique = true
    }
  }
  
  return code!
}

/**
 * 验证邀请码是否有效
 * @param code 邀请码
 * @returns 邀请码信息，如果无效则返回null
 */
export async function validateInviteCode(code: string): Promise<{
  type: 'beta' | 'friend'
  isValid: boolean
  ownerId?: string
} | null> {
  // 检查内测邀请码
  const betaCode = await prisma.betaInviteCode.findUnique({
    where: { code }
  })
  
  if (betaCode) {
    return {
      type: 'beta',
      isValid: !betaCode.isUsed
    }
  }
  
  // 检查好友邀请码
  const friendCode = await prisma.friendInviteCode.findUnique({
    where: { code }
  })
  
  if (friendCode) {
    return {
      type: 'friend',
      isValid: !friendCode.isUsed,
      ownerId: friendCode.ownerId
    }
  }
  
  return null
}

/**
 * 使用邀请码
 * @param code 邀请码
 * @param userId 使用者用户ID
 * @returns 是否成功使用
 */
export async function useInviteCode(code: string, userId: string): Promise<boolean> {
  const validation = await validateInviteCode(code)
  
  if (!validation || !validation.isValid) {
    return false
  }
  
  try {
    if (validation.type === 'beta') {
      await prisma.betaInviteCode.update({
        where: { code },
        data: {
          isUsed: true,
          usedBy: userId,
          usedAt: new Date()
        }
      })
    } else {
      await prisma.friendInviteCode.update({
        where: { code },
        data: {
          isUsed: true,
          usedBy: userId,
          usedAt: new Date()
        }
      })
    }
    
    return true
  } catch (error) {
    console.error('使用邀请码失败:', error)
    return false
  }
}

/**
 * 为用户分配好友邀请码
 * @param userId 用户ID
 * @param count 分配数量
 * @returns 生成的邀请码列表
 */
export async function allocateFriendCodes(userId: string, count: number): Promise<string[]> {
  const codes: string[] = []
  
  for (let i = 0; i < count; i++) {
    const code = await generateUniqueFriendCode()
    
    await prisma.friendInviteCode.create({
      data: {
        code,
        ownerId: userId
      }
    })
    
    codes.push(code)
  }
  
  return codes
}

/**
 * 检查用户是否有权限访问高级关卡
 * @param userType 用户类型
 * @param userScore 用户经验值
 * @param levelName 关卡名称
 * @returns 是否有权限
 */
export function hasAdvancedAccess(userType: string, userScore: number = 0, levelName: string = ''): boolean {
  // 内测用户对所有关卡都有权限
  if (userType === 'beta') {
    return true
  }

  // 邀请用户对"进阶操作"有权限，对"实用技巧"需要600经验值
  if (userType === 'friend') {
    if (levelName === '进阶操作') {
      return true
    }
    if (levelName === '实用技巧') {
      return userScore >= 600
    }
    return true // 对其他关卡有权限
  }

  // 普通用户需要根据经验值判断
  if (userType === 'normal') {
    if (levelName === '进阶操作') {
      return userScore >= 500
    }
    if (levelName === '实用技巧') {
      return userScore >= 600
    }
    return true // 对其他关卡有权限
  }

  return false
}

/**
 * 获取用户类型显示名称
 * @param userType 用户类型
 * @returns 显示名称
 */
export function getUserTypeDisplayName(userType: string): string {
  switch (userType) {
    case 'beta':
      return '内测用户'
    case 'friend':
      return '邀请用户'
    default:
      return '普通用户'
  }
}

/**
 * 获取关卡的解锁状态和显示信息
 * @param levelName 关卡名称
 * @param userType 用户类型
 * @param userScore 用户经验值
 * @returns 解锁状态和显示信息
 */
export function getLevelAccessInfo(levelName: string, userType: string, userScore: number = 0): {
  hasAccess: boolean
  isLocked: boolean
  buttonText: string
  requiredScore?: number
} {
  // 如果不是进阶关卡，直接返回开放状态
  if (levelName !== '进阶操作' && levelName !== '实用技巧') {
    return {
      hasAccess: true,
      isLocked: false,
      buttonText: '开始学习'
    }
  }

  // 内测用户对所有关卡都开放
  if (userType === 'beta') {
    return {
      hasAccess: true,
      isLocked: false,
      buttonText: '专属区域，已对您开放'
    }
  }

  // 邀请用户的权限判断
  if (userType === 'friend') {
    if (levelName === '进阶操作') {
      return {
        hasAccess: true,
        isLocked: false,
        buttonText: '专属区域，已对您开放'
      }
    }
    if (levelName === '实用技巧') {
      if (userScore >= 600) {
        return {
          hasAccess: true,
          isLocked: false,
          buttonText: '专属区域，已对您开放'
        }
      } else {
        return {
          hasAccess: false,
          isLocked: true,
          buttonText: '专属区域，经验值600以上开放',
          requiredScore: 600
        }
      }
    }
  }

  // 普通用户的权限判断
  if (userType === 'normal') {
    if (levelName === '进阶操作') {
      if (userScore >= 500) {
        return {
          hasAccess: true,
          isLocked: false,
          buttonText: '专属区域，已对您开放'
        }
      } else {
        return {
          hasAccess: false,
          isLocked: true,
          buttonText: '专属区域，经验值500以上开放',
          requiredScore: 500
        }
      }
    }
    if (levelName === '实用技巧') {
      if (userScore >= 600) {
        return {
          hasAccess: true,
          isLocked: false,
          buttonText: '专属区域，已对您开放'
        }
      } else {
        return {
          hasAccess: false,
          isLocked: true,
          buttonText: '专属区域，经验值600以上开放',
          requiredScore: 600
        }
      }
    }
  }

  // 默认情况
  return {
    hasAccess: false,
    isLocked: true,
    buttonText: '需要邀请码解锁'
  }
}
