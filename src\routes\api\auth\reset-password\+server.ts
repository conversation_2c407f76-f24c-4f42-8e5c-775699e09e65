import { json } from '@sveltejs/kit'
import type { RequestHandler } from './$types'
import { prisma } from '$lib/db'
import bcrypt from 'bcryptjs'

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { token, password } = await request.json()

    if (!token || !password) {
      return json(
        { error: '令牌和新密码都是必填的' },
        { status: 400 }
      )
    }

    // 验证密码强度
    if (password.length < 6) {
      return json(
        { error: '密码长度至少为6位' },
        { status: 400 }
      )
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: {
        passwordResetToken: token
      }
    })

    if (!user) {
      return json(
        { error: '无效的重置令牌' },
        { status: 400 }
      )
    }

    // 检查令牌是否过期
    if (user.passwordResetExpires && user.passwordResetExpires < new Date()) {
      return json(
        { error: '重置令牌已过期，请重新申请密码重置' },
        { status: 400 }
      )
    }

    // 加密新密码
    const hashedPassword = await bcrypt.hash(password, 12)

    // 更新用户密码并清除重置令牌
    await prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        passwordResetToken: null,
        passwordResetExpires: null
      }
    })

    console.log('密码重置成功:', user.email)

    return json(
      { message: '密码重置成功！您现在可以使用新密码登录了。' },
      { status: 200 }
    )
  } catch (error) {
    console.error('密码重置错误:', error)
    return json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// 验证重置令牌的GET端点
export const GET: RequestHandler = async ({ url }) => {
  try {
    const token = url.searchParams.get('token')

    if (!token) {
      return json(
        { error: '重置令牌缺失' },
        { status: 400 }
      )
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: {
        passwordResetToken: token
      }
    })

    if (!user) {
      return json(
        { error: '无效的重置令牌' },
        { status: 400 }
      )
    }

    // 检查令牌是否过期
    if (user.passwordResetExpires && user.passwordResetExpires < new Date()) {
      return json(
        { error: '重置令牌已过期，请重新申请密码重置' },
        { status: 400 }
      )
    }

    return json(
      { message: '令牌有效', email: user.email },
      { status: 200 }
    )
  } catch (error) {
    console.error('验证重置令牌错误:', error)
    return json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
