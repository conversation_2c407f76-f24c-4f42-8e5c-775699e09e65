# 日志系统使用指南

## 概述

本项目实现了一个智能日志系统，可以根据环境自动控制日志输出级别，确保生产环境中不会输出调试信息，提升用户体验。

## 功能特性

### 1. 环境感知
- **开发环境**: 显示所有日志信息，便于调试
- **生产环境**: 只显示错误信息，保持控制台干净

### 2. 日志级别
- `debug`: 调试信息（仅开发环境）
- `info`: 一般信息
- `warn`: 警告信息
- `error`: 错误信息（始终显示）

### 3. 专用日志方法
- `validation()`: 验证相关日志
- `univer()`: Univer组件相关日志
- `task()`: 任务相关日志

## 使用方法

### 导入日志工具

```typescript
import { log } from '@/app/lib/logger';
```

### 基本用法

```typescript
// 调试信息（仅开发环境显示）
log.debug('这是调试信息', { data: 'some data' });

// 一般信息
log.info('操作完成');

// 警告信息
log.warn('这是一个警告');

// 错误信息（始终显示）
log.error('发生错误', error);
```

### 专用日志方法

```typescript
// 验证相关日志
log.validation('开始验证任务:', rule);

// Univer相关日志
log.univer('Univer实例已准备就绪');

// 任务相关日志
log.task('任务执行完成:', taskId);
```

## 配置

### 环境变量

在 `.env` 文件中配置日志级别：

```env
# 开发环境
NODE_ENV=development
NEXT_PUBLIC_LOG_LEVEL=debug

# 生产环境
NODE_ENV=production
NEXT_PUBLIC_LOG_LEVEL=error
```

### Next.js 配置

在 `next.config.ts` 中配置了生产环境自动移除console语句：

```typescript
compiler: {
  removeConsole: process.env.NODE_ENV === 'production' ? {
    exclude: ['error'] // 保留console.error
  } : false,
}
```

## 迁移指南

### 从 console.log 迁移

原有的 `console.log` 语句已经自动替换为相应的日志方法：

```typescript
// 原来
console.log('开始验证任务:', rule);
console.error('验证失败:', error);

// 现在
log.validation('开始验证任务:', rule);
log.error('验证失败:', error);
```

### 批量替换

项目提供了自动替换脚本：

```bash
node scripts/replace-all-console-logs.js
```

## 测试

### 开发环境测试

```bash
pnpm dev
```

访问应用，控制台会显示详细的调试信息。

### 生产环境测试

```bash
pnpm build
pnpm start
```

访问应用，控制台应该很干净，只显示错误信息（如果有的话）。

### 使用测试页面

打开 `test-production-logs.html` 进行可视化测试。

## 最佳实践

### 1. 选择合适的日志级别

```typescript
// ✅ 好的做法
log.debug('详细的调试信息'); // 仅开发环境
log.info('用户操作完成'); // 重要信息
log.warn('配置可能有问题'); // 警告
log.error('操作失败', error); // 错误

// ❌ 避免的做法
log.error('这只是一个调试信息'); // 错误级别用于非错误信息
```

### 2. 使用专用方法

```typescript
// ✅ 好的做法
log.validation('验证结果:', result);
log.univer('组件状态:', state);

// ❌ 避免的做法
log.debug('验证结果:', result); // 应该使用专用方法
```

### 3. 提供有用的上下文

```typescript
// ✅ 好的做法
log.debug('用户登录', { userId, timestamp, ip });

// ❌ 避免的做法
log.debug('登录'); // 缺少上下文信息
```

## 故障排除

### 1. 日志不显示

检查环境变量配置：
```bash
echo $NODE_ENV
echo $NEXT_PUBLIC_LOG_LEVEL
```

### 2. 生产环境仍有调试信息

确保正确构建了生产版本：
```bash
pnpm build
pnpm start
```

### 3. 导入错误

确保正确导入日志工具：
```typescript
import { log } from '@/app/lib/logger';
```

## 性能影响

- **开发环境**: 无性能影响，所有日志正常输出
- **生产环境**: 
  - console语句在构建时被移除，无运行时开销
  - 日志工具只在需要时执行，性能影响极小

## 总结

这个日志系统提供了：
- 🎯 环境感知的智能日志控制
- 🚀 生产环境零调试信息输出
- 🛠️ 开发环境完整调试支持
- 📝 专用的日志分类方法
- ⚡ 优秀的性能表现

通过使用这个系统，你可以确保应用在生产环境中提供干净的用户体验，同时在开发环境中保持强大的调试能力。
