import { PrismaClient } from '@prisma/client'

import { log } from '@/app/lib/logger';
import { generateUniqueBetaCode, allocateFriendCodes } from '@/app/lib/invite-codes';
const prisma = new PrismaClient()

async function main() {
  // 清空现有数据
  await prisma.userProgress.deleteMany()
  await prisma.task.deleteMany()
  await prisma.level.deleteMany()
  await prisma.friendInviteCode.deleteMany()
  await prisma.betaInviteCode.deleteMany()

  // 定义所有级别和任务的数据结构
  const levelData = [
    // 级别1：初识表格 (105分)
    {
      name: '初识表格',
      description: '学习表格的基本操作和数据输入',
      difficulty: 1,
      order: 1,
      points: 105,
      tasks: [
        { name: '数据输入', points: 10, type: 'input' },
        { name: '粗体和斜体样式', points: 15, type: 'format' },
        { name: '字体更改', points: 15, type: 'format' },
        { name: '字体颜色设置', points: 15, type: 'format' },
        { name: '数字格式', points: 15, type: 'format' },
        { name: '单元格对齐方式', points: 15, type: 'format' },
        { name: '边框设置', points: 20, type: 'format' }
      ]
    },
    // 级别2：基本公式 (100分)
    {
      name: '基本公式',
      description: '掌握基础公式的使用',
      difficulty: 2,
      order: 2,
      points: 100,
      tasks: [
        { name: '数字计算', points: 20, type: 'formula' },
        { name: 'SUM函数', points: 25, type: 'formula' },
        { name: 'AVERAGE函数', points: 25, type: 'formula' },
        { name: 'IF函数', points: 30, type: 'formula' }
      ]
    },
    // 级别3：常用操作 (230分)
    {
      name: '常用操作',
      description: '掌握数据筛选、排序、验证和图表制作',
      difficulty: 3,
      order: 3,
      points: 230,
      tasks: [
        { name: '简单筛选', points: 25, type: 'filter' },
        { name: '复杂筛选', points: 30, type: 'filter' },
        { name: '简单排序', points: 30, type: 'sort' },
        { name: '复杂排序', points: 35, type: 'sort' },
        { name: '简单数据验证', points: 35, type: 'dataValidation' },
        { name: '复杂数据验证', points: 40, type: 'dataValidation' },
        { name: '图表制作', points: 35, type: 'chart' }
      ]
    },
    // 级别4：进阶公式 (100分)
    {
      name: '进阶公式',
      description: '掌握进阶公式的用法',
      difficulty: 4,
      order: 4,
      points: 100,
      tasks: [
        { name: 'VLOOKUP函数', points: 50, type: 'formula' },
        { name: 'INDEX+MATCH组合', points: 50, type: 'formula' }
      ]
    },
    // 级别5：进阶操作 (170分)
    {
      name: '进阶操作',
      description: '使用条件格式、数据透视表等进行高级数据分析',
      difficulty: 5,
      order: 5,
      points: 170,
      tasks: [
        { name: '简单条件格式', points: 50, type: 'conditionalFormat' },
        { name: '复杂条件格式', points: 60, type: 'conditionalFormat' },
        { name: '数据透视表', points: 60, type: 'pivotTable' }
      ]
    },
    // 级别6：实用技巧 (80分)
    {
      name: '实用技巧',
      description: '可能不难，但却很实用的技巧',
      difficulty: 3,
      order: 6,
      points: 80,
      tasks: [
        { name: '单元格合并', points: 20, type: 'merge' },
        { name: '自动换行和强制换行', points: 30, type: 'wrap' },
        { name: '快速填充公式', points: 30, type: 'formula' }
      ]
    }
  ]

  // 任务详细描述和验证逻辑的映射
  const taskDetails: Record<string, {
    description: string;
    validation: string;
    initialData?: string;
  }> = {
    '数据输入': {
      description: '学习在Excel中输入文本数据\n\n操作步骤：\n1. 点击A1单元格（第一行第一列的单元格）\n2. 输入文本"Hello Excel"\n3. 按回车键确认输入\n\n完成后A1单元格应显示：Hello Excel',
      validation: JSON.stringify({
        type: 'cellValue',
        cell: 'A1',
        expectedValue: 'Hello Excel'
      })
    },
    '粗体和斜体样式': {
      description: '学习设置文字的粗体和斜体格式\n\n任务说明：\n表格中已有数据，请使用这些数据进行操作。\n\n操作步骤：\n1. 点击选中B1单元格\n2. 在开始"菜单栏上\n   - 点击粗体按钮（B）设置粗体\n   - 再点击斜体按钮（I）设置斜体\n\n完成后B1单元格中的文字应同时显示为粗体和斜体',
      validation: JSON.stringify({
        type: 'cellStyle',
        cell: 'B1',
        expectedStyle: { bold: true, italic: true }
      }),
      initialData: JSON.stringify({
        'B1': '重要文本'
      })
    },
    '字体更改': {
      description: '学习将字体更改为宋体\n\n任务说明：\n表格中已有数据，请使用这些数据进行操作。\n\n操作步骤：\n1. 点击选中B1单元格\n2. 在"开始"菜单栏上\n3. 点击字体名称下拉框（默认显示当前字体名）\n4. 在下拉列表中选择"宋体"\n\n完成后B1单元格的字体应变为宋体',
      validation: JSON.stringify({
        type: 'cellStyle',
        cell: 'B1',
        expectedStyle: {
          fontFamily: '宋体'
        }
      }),
      initialData: JSON.stringify({
        'B1': '宋体文本'
      })
    },
    '字体颜色设置': {
      description: '学习设置字体颜色和单元格背景颜色\n\n任务说明：\n表格中已有数据，请使用这些数据进行操作。\n\n操作步骤：\n1. 点击选中A1单元格\n2. 在"开始"菜单栏上\n   - 点击字体颜色按钮（A图标旁的下拉箭头），选择红色（颜色选择框里的第3行第3列）\n   -  点击填充颜色按钮（油漆桶图标），选择黄色（颜色选择框里的第2行第5列）\n\n完成后A1单元格应显示红色字体，黄色背景',
      validation: JSON.stringify({
        type: 'cellStyle',
        cell: 'A1',
        expectedStyle: {
          color: '#f05252',
          backgroundColor: '#fac815'
        }
      }),
      initialData: JSON.stringify({
        'A1': '彩色文本'
      })
    },
    '数字格式': {
      description: '学习设置单元格的数字格式\n\n任务说明：\n表格中已有数据，请使用这些数据进行操作。\n\n操作步骤：\n1. 点击选中A1单元格\n2. 在菜单栏上点击最左边的菜单切换按钮，从下拉列表中选择"数据"，切换到"数据"菜单\n3. 在菜单栏上选择"货币"格式\n\n完成后A1单元格应显示为货币格式（￥1,234.56或$1,234.56）',
      validation: JSON.stringify({
        type: 'cellFormat',
        cell: 'A1',
        expectedFormat: 'currency'
      }),
      initialData: JSON.stringify({
        'A1': 1234.56
      })
    },
    '单元格对齐方式': {
      description: '学习设置单元格的对齐方式\n\n任务说明：\n表格中已有数据，请使用这些数据进行操作。\n\n操作步骤：\n1. 选中A1单元格，设置为左对齐：\n   - 在"开始"菜单栏上\n   - 找到对齐方式设置选项，从下拉菜单中选择"左对齐"按钮\n2. 选中B1单元格，设置为居中对齐：\n   - 在"开始"菜单栏上\n   - 在对齐方式设置选项中点击"居中对齐"按钮\n3. 选中C1单元格，设置为右对齐：\n   - 在"开始"菜单栏上\n   - 在对齐方式设置选项中点击"右对齐"按钮\n\n完成后三个单元格应分别显示左对齐、居中对齐和右对齐',
      validation: JSON.stringify({
        type: 'multiCellAlignment',
        cells: [
          { cell: 'A1', expectedAlignment: 'left' },
          { cell: 'B1', expectedAlignment: 'center' },
          { cell: 'C1', expectedAlignment: 'right' }
        ]
      }),
      initialData: JSON.stringify({
        'A1': '左对齐',
        'B1': '居中对齐',
        'C1': '右对齐'
      })
    },
    '边框设置': {
      description: '学习设置不同的边框样式\n\n任务说明：\n表格中已有数据，请使用这些数据进行操作。\n\n操作步骤：\n1. 拖动鼠标选中A1:B2区域，设置外边框：\n   - 在"开始"菜单上，找到网格形状的按钮\n   - 点击该按钮，从弹窗窗口中选择第2行第2个图标，即"外边框"（口字型）\n\n2. 选中A4:B5区域，设置所有边框：\n   - 点击工具栏"开始"菜单，找到网格形状的按钮\n   - 点击该按钮，打开边框设置窗口。从弹窗窗口中选择第1列第2个图标（田字型），即"所有边框"\n\n3. 选中A7:B8区域，设置蓝色粗边框：\n   - 打开边框设置窗口。从弹窗窗口中选择第2行第2个图标\n    - 设置颜色：打开边框设置窗口，在颜色框中选择第2列第4行的蓝色\n   - 再设置边框粗细：打开边框设置窗口，点击底部最右边的下拉菜单，选择第7根粗线\n\n完成后三个区域应分别显示：外边框、所有边框、蓝色粗边框',
      validation: JSON.stringify({
        type: 'multiBorder',
        cells: [
          {
            range: 'A1:B2',
            expectedBorder: 'outline',
            description: '外边框区域（2行2列）'
          },
          {
            range: 'A4:B5',
            expectedBorder: 'all',
            description: '所有边框区域（2行2列）'
          },
          {
            range: 'A7:B8',
            expectedBorder: 'thick',
            expectedBorderColor: '#1a56db',
            description: '蓝色粗边框区域（2行2列）'
          }
        ]
      }),
      initialData: JSON.stringify({
        'A1': '外边框',
        'B1': '区域',
        'A2': '示例1',
        'B2': '数据1',
        'A4': '所有边框',
        'B4': '区域',
        'A5': '示例2',
        'B5': '数据2',
        'A7': '蓝色粗边框',
        'B7': '区域',
        'A8': '示例3',
        'B8': '数据3'
      })
    },
    '数字计算': {
      description: '学习在Excel中进行基本的数字计算\n\n操作步骤：\n1. 点击B1单元格，输入数字10，按回车确认\n2. 点击B2单元格，输入数字20，按回车确认\n3. 点击B3单元格，输入公式=B1+B2，按回车确认\n\n完成后：\n- B1单元格显示：10\n- B2单元格显示：20\n- B3单元格显示：30（计算结果）',
      validation: JSON.stringify({
        type: 'cellFormula',
        cell: 'B3',
        expectedFormula: '=B1+B2',
        expectedValue: 30
      })
    },
    'SUM函数': {
      description: '学习使用SUM函数计算一组数字的总和\n\n任务说明：\n表格中已有数据，请使用这些数据进行操作。\n\n操作步骤：\n1. 点击A6单元格（第6行第1列）\n2. 输入公式=SUM(A1:A5)\n   - 注意：公式中的等号、冒号和括号都需要是英文标点符号\n3. 按回车键确认\n\n完成后A6单元格应显示：150（A1到A5的总和）\n\n提示：SUM函数可以快速计算指定范围内所有数字的总和',
      validation: JSON.stringify({
        type: 'cellFormula',
        cell: 'A6',
        expectedFormula: '=SUM(A1:A5)'
      }),
      initialData: JSON.stringify({
        'A1': 10,
        'A2': 20,
        'A3': 30,
        'A4': 40,
        'A5': 50
      })
    },
    'AVERAGE函数': {
      description: '学习使用AVERAGE函数计算一组数字的平均值\n\n任务说明：\n表格中已有数据，请使用这些数据进行操作。\n\n操作步骤：\n1. 点击B6单元格（第6行第2列）\n2. 输入公式=AVERAGE(B1:B5)\n   - 注意：公式中的等号、冒号和括号都需要是英文标点符号\n3. 按回车键确认\n\n完成后B6单元格应显示：85（B1到B5的平均值）\n\n提示：AVERAGE函数可以自动计算指定范围内所有数字的平均值',
      validation: JSON.stringify({
        type: 'cellFormula',
        cell: 'B6',
        expectedFormula: '=AVERAGE(B1:B5)'
      }),
      initialData: JSON.stringify({
        'B1': 80,
        'B2': 90,
        'B3': 85,
        'B4': 95,
        'B5': 75
      })
    },
    'IF函数': {
      description: '学习使用IF函数进行条件判断\n\n任务说明：\n表格中已有数据，请使用这些数据进行操作。\n\n操作步骤：\n1. 点击B1单元格\n2. 输入公式=IF(A1>10,"大","小")\n   - 注意：公式中的括号、逗号、大于号和引号都需要是英文标点符号\n3. 按回车键确认\n\n公式解释：\n- IF(条件, 真值, 假值)\n- 如果A1>10为真，显示"大"\n- 如果A1>10为假，显示"小"\n\n完成后B1单元格应显示：大（因为15>10）',
      validation: JSON.stringify({
        type: 'cellFormula',
        cell: 'B1',
        expectedFormula: '=IF(A1>10,"大","小")'
      }),
      initialData: JSON.stringify({
        'A1': 15
      })
    },
    '简单筛选': {
      description: '学习对单列数据进行筛选\n\n任务说明：\n表格中已有员工数据，请对部门列进行筛选，只显示"销售部"的员工。\n\n操作步骤：\n1. 选择数据范围A1:C6（包含表头和所有数据）\n2. 在菜单栏上点击最左边的菜单切换按钮，从下拉列表中选择"数据"，切换到"数据"菜单\n3. 在"数据"菜单上点击"筛选"按钮（或使用快捷键Ctrl+Shift+L）\n4. 点击B1单元格（部门列）右侧出现的筛选图标\n5. 在筛选菜单中：\n   - 取消选中"全选"\n   - 只勾选"销售部"\n   - 点击"确定"\n\n验证要求：\n- 筛选后应只显示3行数据（表头+2个销售部员工）\n- 显示的员工应为：张三、王五\n- 其他部门的员工应被隐藏\n\n提示：\n- 筛选不会删除数据，只是暂时隐藏不符合条件的行\n- 可以随时清除筛选恢复所有数据的显示',
      validation: JSON.stringify({
        type: 'filter',
        dataRange: 'A1:C6',
        expectedVisibleRows: 2,
        expectedFilteredData: [
          { 0: '张三', 1: '销售部', 2: 8000 },
          { 0: '王五', 1: '销售部', 2: 9000 }
        ]
      }),
      initialData: JSON.stringify({
        'A1': '姓名',
        'B1': '部门',
        'C1': '工资',
        'A2': '张三',
        'B2': '销售部',
        'C2': 8000,
        'A3': '李四',
        'B3': '技术部',
        'C3': 12000,
        'A4': '王五',
        'B4': '销售部',
        'C4': 9000,
        'A5': '赵六',
        'B5': '财务部',
        'C5': 7500,
        'A6': '钱七',
        'B6': '人事部',
        'C6': 6800
      })
    },
    '复杂筛选': {
      description: '学习对多列数据进行组合筛选\n\n任务说明：\n表格中已有产品销售数据，请同时筛选地区和季度，只显示"北京"地区"Q1"季度的数据。\n\n操作步骤：\n1. 选择数据范围A1:D7（包含表头和所有数据）\n2. 切换到"数据"菜单，点击“筛选”，启用筛选功能\n3. 设置地区筛选：\n   - 点击B1单元格（地区列）右侧的筛选图标\n   - 取消"全选"，只勾选"北京"\n   - 点击"确定"\n4. 设置季度筛选：\n   - 点击C1单元格（季度列）右侧的筛选图标\n   - 取消"全选"，只勾选"Q1"\n   - 点击"确定"\n\n验证要求：\n- 筛选后应只显示2行数据（表头+1个符合条件的记录）\n- 显示的产品应为：笔记本\n- 销售额应为：15000\n\n提示：\n- 多列筛选是AND关系，必须同时满足所有条件\n- 筛选顺序不影响结果',
      validation: JSON.stringify({
        type: 'filter',
        dataRange: 'A1:D7',
        expectedVisibleRows: 2,
        expectedFilteredData: [
          { 0: '笔记本', 1: '北京', 2: 'Q1', 3: 15000 },
          { 0: '手机', 1: '北京', 2: 'Q1', 3: 22000 }
        ]
      }),
      initialData: JSON.stringify({
        'A1': '产品',
        'B1': '地区',
        'C1': '季度',
        'D1': '销售额',
        'A2': '笔记本',
        'B2': '北京',
        'C2': 'Q1',
        'D2': 15000,
        'A3': '台式机',
        'B3': '上海',
        'C3': 'Q1',
        'D3': 12000,
        'A4': '笔记本',
        'B4': '广州',
        'C4': 'Q2',
        'D4': 18000,
        'A5': '台式机',
        'B5': '北京',
        'C5': 'Q2',
        'D5': 14000,
        'A6': '平板',
        'B6': '上海',
        'C6': 'Q1',
        'D6': 8000,
        'A7': '手机',
        'B7': '北京',
        'C7': 'Q1',
        'D7': 22000
      })
    },
    '简单排序': {
      description: '学习对单列数据进行排序\n\n任务说明：\n表格中已有学生成绩数据，请按成绩从高到低进行排序。\n\n操作步骤：\n1. 选择数据范围A1:C6（包含表头和所有数据）\n2. 切换到"数据"菜单\n3. 点击"排序"按钮，选择"自定义排序"\n4. 在排序提醒对话框中选择"拓展排序范围"，点击"确认"\n5. 在弹窗的对话框中选中"标题不参与排序"\n6. 排序关键字选择"成绩"，次序选择"降序"，点击"确定"\n\n验证要求：\n- 排序后第一行应为：李四（95分）\n- 排序后最后一行应为：赵六（78分）\n- 数据行的完整性应保持（姓名、班级、成绩对应关系不变）\n\n提示：\n- 排序会重新排列数据行的顺序\n- 确保选择完整的数据范围，避免数据错位',
      validation: JSON.stringify({
        type: 'sort',
        column: 'C',
        direction: 'desc',
        expectedOrder: ['李四', '王五', '钱七', '张三', '赵六']
      }),
      initialData: JSON.stringify({
        'A1': '姓名',
        'B1': '班级',
        'C1': '成绩',
        'A2': '张三',
        'B2': '一班',
        'C2': 85,
        'A3': '李四',
        'B3': '二班',
        'C3': 95,
        'A4': '王五',
        'B4': '一班',
        'C4': 92,
        'A5': '赵六',
        'B5': '三班',
        'C5': 78,
        'A6': '钱七',
        'B6': '二班',
        'C6': 88
      })
    },
    '复杂排序': {
      description: '学习对多列数据进行组合排序\n\n任务说明：\n表格中已有员工数据，请先按部门升序排序，再按工资降序排序。\n\n操作步骤：\n1. 选择数据范围A1:C7（包含表头和所有数据）\n2. 切换到"数据"菜单，点击"排序"按钮，选择"自定义排序"\n3. 在排序提醒对话框中选择"拓展排序范围"，点击"确认"\n4. 在弹窗的对话框中选中"标题不参与排序"\n5. 设置第一排序关键字：\n   - 选择"部门"\n   - 选择"升序"\n6. 添加第二排序关键字：\n   - 点击"添加排序条件按钮\n   - 选择"工资"\n   - 次序选择"降序"\n5. 点击"确定"\n\n验证要求：\n- 人事部员工应排在最前面（这里是按笔画排序）\n- 同部门内按工资从高到低排序\n- 预期顺序：钱七(人事)→孙八(人事)→李四(技术)→赵六(财务)→王五(销售)→张三(销售)\n\n提示：\n- 多级排序按优先级执行\n- 第一关键字相同时，按第二关键字排序',
      validation: JSON.stringify({
        type: 'multiSort',
        sorts: [
          { column: 'B', direction: 'asc' },
          { column: 'C', direction: 'desc' }
        ],
        expectedOrder: ['钱七', '孙八', '李四', '赵六', '王五', '张三']
      }),
      initialData: JSON.stringify({
        'A1': '姓名',
        'B1': '部门',
        'C1': '工资',
        'A2': '张三',
        'B2': '销售部',
        'C2': 8000,
        'A3': '李四',
        'B3': '技术部',
        'C3': 12000,
        'A4': '王五',
        'B4': '销售部',
        'C4': 9000,
        'A5': '赵六',
        'B5': '财务部',
        'C5': 7500,
        'A6': '钱七',
        'B6': '人事部',
        'C6': 6800,
        'A7': '孙八',
        'B7': '人事部',
        'C7': 6500
      })
    },
    '简单数据验证': {
      description: '学习设置下拉菜单数据验证（自定义选项）\n\n任务说明：\n为B1单元格设置数据验证，创建一个包含自定义选项的下拉菜单。\n\n操作步骤：\n1. 点击选中B1单元格\n2. 切换到"数据"菜单\n3. 在"数据"菜单上点击"数据验证"\n4. 点击"新建规则"\n5. 条件类型选择"下拉菜单"\n6. 选择"自定义"\n7. 点击"添加选项"，输入"优秀,良好,一般,较差"（分隔号是用英文逗号）或依次添加以下选项：\n   - 优秀\n   - 良好\n   - 一般\n   - 较差\n8. 点击"确定"\n\n完成后B1单元格应显示下拉箭头，点击可选择预设选项',
      validation: JSON.stringify({
        type: 'dataValidation',
        cell: 'B1',
        validationType: 'list',
        source: '优秀,良好,一般,较差',
        allowDropdown: true
      }),
      initialData: JSON.stringify({
        'A1': '评价等级：',
        'B1': ''
      })
    },
    '复杂数据验证': {
      description: '学习设置下拉菜单数据验证（引用数据）\n\n任务说明：\n表格中已有部门数据，为B1单元格设置数据验证，引用现有数据作为下拉选项。\n\n操作步骤：\n1. 点击选中B1单元格\n2. 切换到"数据"菜单\n3. 在"数据"菜单上点击"数据验证"\n4. 点击"新建规则"\n5. 条件类型选择"下拉菜单"\n6. 选择"引用数据"\n7. 点击=号后所在的输入区域，在=号后输入引用的单元格区间：D1:D4或鼠标拖拽选择D1到D4\n8. 点击"确定"\n\n完成后B1单元格应显示下拉箭头，选项来自D列数据',
      validation: JSON.stringify({
        type: 'dataValidation',
        cell: 'B1',
        validationType: 'list',
        source: '$D$1:$D$4',
        allowDropdown: true
      }),
      initialData: JSON.stringify({
        'A1': '选择部门：',
        'B1': '',
        'D1': '销售部',
        'D2': '技术部',
        'D3': '市场部',
        'D4': '财务部'
      })
    },
    '图表制作': {
      description: '学习创建柱状图来可视化数据\n\n任务说明：\n表格中已有月度销量数据，请创建柱状图进行可视化展示。\n\n操作步骤：\n1. 选择数据范围A1:B6（包含表头"月份"、"销量"和所有数据）\n2. 在菜单栏上点击最左边的菜单切换按钮，从下拉列表中选择"插入"，切换到"插入"菜单\n3. 在"插入"菜单上，点击"插入图表"\n4. 选择任意一种图表样式（推荐选择"柱状图"）\n5. 图表会自动插入到工作表中\n6. 可以拖拽调整图表位置和大小\n\n提示：\n- 确保选择的数据范围包含表头\n- 图表创建成功后系统会自动检测\n- 图表类型不限，柱状图、条形图都可以',
      validation: JSON.stringify({
        type: 'chart',
        expectedType: 'column',
        dataRange: 'A1:B6'
      }),
      initialData: JSON.stringify({
        'A1': '月份',
        'B1': '销量',
        'A2': '一月',
        'A3': '二月',
        'A4': '三月',
        'A5': '四月',
        'A6': '五月',
        'B2': 120,
        'B3': 150,
        'B4': 180,
        'B5': 200,
        'B6': 160
      })
    },
    'VLOOKUP函数': {
      description: '学习使用VLOOKUP函数进行数据查找\n\n任务说明：\n表格中已有水果价格数据，请使用VLOOKUP函数查找指定水果的价格。\n\n操作步骤：\n1. 点击E2单元格\n2. 输入公式=VLOOKUP(D2,A:B,2,FALSE)\n3. 按回车键确认\n\n公式解释：\n- VLOOKUP(查找值, 查找范围, 列号, 精确匹配)\n- D2是要查找的值（橙子）\n- A:B是查找的数据范围（水果名称和价格）\n- 2表示返回第2列的值（价格）\n- FALSE表示精确匹配，也可以用0代替\n\n完成后E2单元格应显示：4.8（橙子的价格）',
      validation: JSON.stringify({
        type: 'cellFormula',
        cell: 'E2',
        expectedFormula: '=VLOOKUP(D2,A:B,2,FALSE)'
      }),
      initialData: JSON.stringify({
        'A1': '苹果',
        'A2': '香蕉',
        'A3': '橙子',
        'A4': '葡萄',
        'A5': '西瓜',
        'A6': '草莓',
        'A7': '芒果',
        'A8': '菠萝',
        'A9': '猕猴桃',
        'A10': '樱桃',
        'B1': 5.5,
        'B2': 3.2,
        'B3': 4.8,
        'B4': 8.0,
        'B5': 6.5,
        'B6': 12.0,
        'B7': 7.5,
        'B8': 9.0,
        'B9': 10.5,
        'B10': 15.0,
        'D1': '水果种类',
        'D2': '橙子',
        'E1': '查找价格'
      })
    },
    'INDEX+MATCH组合': {
      description: '学习使用INDEX和MATCH函数组合进行高级查找\n\n任务说明：\n表格中已有员工部门数据，请使用INDEX+MATCH组合查找指定人员的部门。\n\n操作步骤：\n1. 点击F2单元格\n2. 输入公式=INDEX(A:A,MATCH(E2,B:B,0))\n3. 按回车键确认\n\n公式解释：\n- MATCH(E2,B:B,0)：在B列（人员列）中查找E2的值（赵六）的位置；0表示精确匹配\n- INDEX(A:A,位置)：返回A列（部门列）中对应位置的值\n- 这种组合比VLOOKUP更灵活，可以向左查找，不受列顺序限制\n\n完成后F2单元格应显示：人事部（赵六所在的部门）',
      validation: JSON.stringify({
        type: 'cellFormula',
        cell: 'F2',
        expectedFormula: '=INDEX(A:A,MATCH(E2,B:B,0))'
      }),
      initialData: JSON.stringify({
        'A1': '销售部',
        'A2': '技术部',
        'A3': '财务部',
        'A4': '人事部',
        'A5': '市场部',
        'A6': '运营部',
        'A7': '客服部',
        'A8': '采购部',
        'A9': '法务部',
        'A10': '行政部',
        'B1': '张三',
        'B2': '李四',
        'B3': '王五',
        'B4': '赵六',
        'B5': '钱七',
        'B6': '孙八',
        'B7': '周九',
        'B8': '吴十',
        'B9': '郑一',
        'B10': '王二',
        'E1': '人员',
        'E2': '赵六',
        'F1': '查找部门'
      })
    },
    '简单条件格式': {
      description: '学习为数值设置条件格式\n\n任务说明：\n表格中已有销售数据，请为销售额大于10000的单元格设置红色背景。\n\n操作步骤：\n1. 选择数据范围C2:C6（销售额数据，不包含表头）\n2. 切换到"数据"菜单\n3. 在"数据"菜单上点击"条件格式"\n4. 选择"突出显示单元格规则" → 将样式规则设置为"数值" → "大于"\n5. 继续在对话框中：\n   - 输入条件值：10000\n   - 在"填充"颜色选择框（油漆桶图标）中选择第3行第3列的红色\n   - 点击"确定"\n\n验证要求：\n- C3单元格（12000）应显示红色背景\n- C5单元格（15000）应显示红色背景\n- 其他销售额单元格保持原样\n\n💡 提示：\n- 条件格式会根据数据变化自动更新\n- 可以设置多个条件格式规则',
      validation: JSON.stringify({
        type: 'conditionalFormat',
        range: 'C2:C6',
        condition: 'greaterThan',
        value: 10000,
        expectedFormattedCells: ['C3', 'C5'],
        expectedBackgroundColor: '#f05252'
      }),
      initialData: JSON.stringify({
        'A1': '产品',
        'B1': '月份',
        'C1': '销售额',
        'A2': '笔记本',
        'B2': '1月',
        'C2': 8500,
        'A3': '台式机',
        'B3': '1月',
        'C3': 12000,
        'A4': '平板',
        'B4': '1月',
        'C4': 6800,
        'A5': '手机',
        'B5': '1月',
        'C5': 15000,
        'A6': '耳机',
        'B6': '1月',
        'C6': 3200
      })
    },
    '复杂条件格式': {
      description: '学习设置多个条件格式规则\n\n任务说明：\n表格中已有学生成绩数据，请设置以下条件格式：\n- 成绩≥90：绿色背景\n- 成绩60-89：黄色背景\n- 成绩<60：红色背景\n\n操作步骤：\n1. 选择数据范围C2:C7（成绩数据）\n2. 设置第一个条件（优秀）：\n   - 点击"数据"菜单中的"条件格式"按钮\n   - 选择"突出显示单元格规则" → 样式规则设置为"数值" → "大于等于"\n   - 输入90，在背景填充颜色选择框（油漆桶图标）中选择第6列第3行的绿色\n   - 点击"确定"\n3. 设置第二个条件（及格）：\n   - 再次点击"条件格式" → "突出显示单元格规则" → 样式规则设置为"数值" → "介于"\n   - 输入60到89，在背景填充颜色选择框第5列第2行的黄色\n   - 点击"确定"\n4. 设置第三个条件（不及格）：\n   - 点击"条件格式" → "突出显示单元格规则" → 样式规则设置为"数值" → "小于"\n   - 输入60，在背景填充颜色选择框中选择第3列第3行的红色\n   - 点击"确定"\n\n验证要求：\n- 95分、92分应显示绿色背景\n- 85分、78分、88分应显示黄色背景\n- 45分应显示红色背景\n\n💡 提示：\n- 条件格式按优先级执行，后设置的规则优先级更高\n- 可以通过"管理规则"调整优先级',
      validation: JSON.stringify({
        type: 'multiConditionalFormat',
        range: 'C2:C7',
        conditions: [
          { type: 'greaterThanOrEqual', value: 90, color: '#0da471' },
          { type: 'between', minValue: 60, maxValue: 89, color: '#fac815' },
          { type: 'lessThan', value: 60, color: '#f05252' }
        ],
        expectedResults: {
          '#0da471': ['C3', 'C4'],
          '#fac815': ['C2', 'C5', 'C6'],
          '#f05252': ['C7']
        }
      }),
      initialData: JSON.stringify({
        'A1': '姓名',
        'B1': '班级',
        'C1': '成绩',
        'A2': '张三',
        'B2': '一班',
        'C2': 85,
        'A3': '李四',
        'B3': '二班',
        'C3': 95,
        'A4': '王五',
        'B4': '一班',
        'C4': 92,
        'A5': '赵六',
        'B5': '三班',
        'C5': 78,
        'A6': '钱七',
        'B6': '二班',
        'C6': 88,
        'A7': '孙八',
        'B7': '三班',
        'C7': 45
      })
    },
    '数据透视表': {
      description: '学习创建数据透视表来分析数据\n\n任务说明：\n表格中已有销售数据，请创建数据透视表进行分析。本任务要求创建包含特定内容的透视表。\n\n操作步骤：\n1. 选择数据范围A1:D6（包含表头和所有数据）\n2. 按鼠标右键（或从"数据"菜单上），选择"数据透视表"\n3. 在弹出的对话框中：\n   - 确认数据范围为"工作表1!A1:D6"\n   - 选择放置位置为新工作表\n   - 点击"确定"\n4. 点击值/行/列/筛选中的任一区域，打开透视表字段面板：\n   - 将"产品"字段拖拽到行区域(或右键点击"产品"字段，选择添加到行)\n   - 将"地区"字段拖拽到列区域(或右键点击"地区"字段，选择添加到列)\n   - 将"销售额"字段拖拽到值区域(或右键点击"销售额"字段，选择添加到值)，完成透视表的设置\n\n\n验证要求：\n- 行标题必须包含：笔记本、台式机、总计\n- 列标题必须包含：北京、上海、广州、总计\n- 总计值应为：75000\n- 透视表必须包含产品、地区、销售额三个字段\n\n提示：\n- 本任务采用严格验证模式，需要完整配置透视表字段\n- 确保透视表包含所有必需的行标题、列标题和正确的总计值\n- 如果验证失败，请检查字段配置是否正确',
      validation: JSON.stringify({
        type: 'pivotTable',
        expectedFields: ['产品', '地区', '销售额'],
        expectedRowHeaders: ['笔记本', '台式机', '总计'],
        expectedColumnHeaders: ['北京', '上海', '广州', '总计'],
        expectedTotalValue: 75000,
        strictValidation: true
      }),
      initialData: JSON.stringify({
        'A1': '产品',
        'B1': '地区',
        'C1': '季度',
        'D1': '销售额',
        'A2': '笔记本',
        'B2': '北京',
        'C2': 'Q1',
        'D2': 15000,
        'A3': '台式机',
        'B3': '上海',
        'C3': 'Q1',
        'D3': 12000,
        'A4': '笔记本',
        'B4': '广州',
        'C4': 'Q2',
        'D4': 18000,
        'A5': '台式机',
        'B5': '北京',
        'C5': 'Q2',
        'D5': 14000,
        'A6': '笔记本',
        'B6': '上海',
        'C6': 'Q3',
        'D6': 16000
      })
    },
    '单元格合并': {
      description: '学习在Excel中合并单元格\n\n任务说明：\n表格中已预填了一些数据，请按要求合并指定的单元格。\n\n操作步骤：\n1. 水平合并：选中B1、C1和D1单元格（可以选中B1然后拖拽选择，或按住Shift键逐个点击）\n2. 在"开始"菜单中找到"合并单元格"按钮，点击下拉箭头\n3. 从下拉菜单中选择"水平合并"或"全部合并"，在弹出的警告窗口中点击"确定"\n4. 垂直合并：选中B3和B4单元格\n5. 使用"合并单元格"下拉菜单中的"垂直合并"或"全部合并"进行合并，在弹出的警告窗口中点击"确定"\n\n验证要求：\n- B1:D1范围必须是合并状态\n- B3:B4范围必须是合并状态\n\n提示：\n- 合并单元格会保留左上角单元格的内容\n- 可以使用"合并单元格"下拉菜单中的"取消合并"撤销单元格的合并',
      validation: JSON.stringify({
        type: 'cellMerge',
        mergedRanges: [
          { range: 'B1:D1', description: '水平合并B1到D1' },
          { range: 'B3:B4', description: '垂直合并B3到B4' }
        ]
      }),
      initialData: JSON.stringify({
        'B1': '水平合并',
        'C1': '示例',
        'D1': '文本',
        'B3': '垂直合并',
        'B4': '示例文本'
      })
    },
    '自动换行和强制换行': {
      description: '学习在Excel中设置文本换行\n\n任务说明：\n表格中已预填了一些数据，请按要求设置文本换行。\n\n操作步骤：\n1. 自动换行：选择B1单元格\n2. 在"开始"菜单中找到"文本换行"按钮，点击选择"自动换行"\n3. 强制换行：双击B2单元格进入编辑模式\n4. 将光标定位到"Excel"和"学习"之间\n5. 按Alt+Enter键(回车键)插入强制换行符\n6. 按Enter键确认编辑\n\n验证要求：\n- B1单元格必须设置为自动换行\n- B2单元格内容必须包含换行符，显示为两行文本\n\n提示：\n- 自动换行会根据列宽自动调整文本显示\n- 强制换行使用Alt+Enter在指定位置插入换行符',
      validation: JSON.stringify({
        type: 'textWrap',
        cells: [
          {
            cell: 'B1',
            wrapType: 'auto',
            description: '自动换行设置'
          },
          {
            cell: 'B2',
            wrapType: 'manual',
            expectedText: 'Excel\n学习应用',
            description: '强制换行内容'
          }
        ]
      }),
      initialData: JSON.stringify({
        'B1': '这是一段很长的文本内容……',
        'B2': 'Excel学习应用'
      })
    },
    '快速填充公式': {
      description: '学习使用快速填充功能批量输入公式\n\n任务说明：\n表格中已预填了数量和单价数据，请使用快速填充功能为所有行计算成本。\n\n操作步骤：\n1. 点击C2单元格\n2. 输入公式：=A2*B2\n3. 按回车键确认公式\n4. 再次选中C2单元格\n5. 将鼠标移动到C2单元格的右下角，直到出现十字符号（填充柄）\n6. 方法一：按住鼠标左键，向下拖拽至C11单元格，然后松开鼠标\n7. 方法二：在出现十字符号时，双击鼠标左键实现快速填充\n\n验证要求：\n- C2:C11所有单元格都必须包含正确的乘法公式\n- 每个单元格的公式应该是对应行的数量乘以单价（如C3应为=A3*B3）\n- 所有公式的计算结果必须正确\n\n提示：\n- 快速填充会自动调整公式中的单元格引用\n- 填充柄是Excel中非常实用的功能，可以快速复制公式和数据',
      validation: JSON.stringify({
        type: 'formulaFill',
        range: 'C2:C11',
        expectedFormulas: [
          { cell: 'C2', formula: '=A2*B2' },
          { cell: 'C3', formula: '=A3*B3' },
          { cell: 'C4', formula: '=A4*B4' },
          { cell: 'C5', formula: '=A5*B5' },
          { cell: 'C6', formula: '=A6*B6' },
          { cell: 'C7', formula: '=A7*B7' },
          { cell: 'C8', formula: '=A8*B8' },
          { cell: 'C9', formula: '=A9*B9' },
          { cell: 'C10', formula: '=A10*B10' },
          { cell: 'C11', formula: '=A11*B11' }
        ]
      }),
      initialData: JSON.stringify({
        'A1': '数量',
        'B1': '单价',
        'C1': '成本',
        'A2': 10,
        'B2': 25,
        'A3': 15,
        'B3': 30,
        'A4': 8,
        'B4': 45,
        'A5': 20,
        'B5': 35,
        'A6': 12,
        'B6': 28,
        'A7': 18,
        'B7': 40,
        'A8': 25,
        'B8': 22,
        'A9': 14,
        'B9': 38,
        'A10': 22,
        'B10': 33,
        'A11': 16,
        'B11': 42
      })
    }
  }

  // 任务描述映射
  const taskDescriptions: Record<string, string> = {
    '数据输入': '迈出一小步，在Excel单元格中输入内容',
    '粗体和斜体样式': '学会使用粗体和斜体，让重要文字更突出',
    '字体更改': '学会变换字体，让字体样式更合你的品味',
    '字体颜色设置': '运用颜色搭配，让数据更直观醒目',
    '数字格式': '学会设置货币、百分比等数字格式',
    '单元格对齐方式': '掌握文字对齐，让表格布局更整齐',
    '边框设置': '学会添加边框，让表格结构更清晰',
    '数字计算': '掌握Excel基本运算，开启公式之旅',
    'SUM函数': '学会快速求和，告别手工计算',
    'AVERAGE函数': '轻松计算平均值，数据分析第一步',
    'IF函数': '掌握条件判断，让Excel变得更智能',
    '简单筛选': '快速找到需要的数据，提升工作效率',
    '复杂筛选': '掌握多条件筛选，精准定位目标数据',
    '简单排序': '让数据井然有序，一键搞定排序',
    '复杂排序': '多级排序技巧，处理复杂数据结构',
    '简单数据验证': '设置下拉菜单，规范数据输入',
    '复杂数据验证': '引用数据源，创建动态验证规则',
    '图表制作': '将枯燥数据变成直观图表',
    'VLOOKUP函数': '掌握查找神器，快速匹配数据',
    'INDEX+MATCH组合': '更灵活的查找方案，突破VLOOKUP限制',
    '简单条件格式': '让数据自动变色，异常值一目了然',
    '复杂条件格式': '多重条件着色，打造专业数据报表',
    '数据透视表': '数据分析利器，轻松汇总统计',
    '单元格合并': '合并单元格技巧，优化表格布局',
    '自动换行和强制换行': '文本换行设置，让内容显示更完整',
    '快速填充公式': '批量应用公式，大幅提升操作效率'
  }

  // 创建级别和任务
  for (const levelInfo of levelData) {
    const { tasks, ...levelData } = levelInfo

    // 创建主级别
    const level = await prisma.level.create({
      data: {
        ...levelData,
        isMainTask: true
      }
    })

    // 为每个任务创建子级别
    for (let i = 0; i < tasks.length; i++) {
      const taskInfo = tasks[i]

      // 创建子级别
      const subLevel = await prisma.level.create({
        data: {
          name: taskInfo.name,
          description: taskDescriptions[taskInfo.name] || `学习${taskInfo.name}`,
          difficulty: levelInfo.difficulty,
          order: i + 1,
          points: taskInfo.points,
          parentId: level.id,
          isMainTask: false
        }
      })

      // 创建任务
      const taskDetail = taskDetails[taskInfo.name] || {
        description: `学习${taskInfo.name}的使用方法`,
        validation: JSON.stringify({ type: 'basic' })
      }

      await prisma.task.create({
        data: {
          name: taskInfo.name,
          description: taskDetail.description,
          type: taskInfo.type,
          order: 1,
          validation: taskDetail.validation,
          initialData: taskDetail.initialData || null,
          levelId: subLevel.id
        }
      })
    }
  }

  // 生成100个内测邀请码
  log.debug('开始生成内测邀请码...')
  for (let i = 0; i < 100; i++) {
    const code = await generateUniqueBetaCode()
    await prisma.betaInviteCode.create({
      data: {
        code
      }
    })
  }
  log.debug('内测邀请码生成完成！')

  // 为现有用户分配好友邀请码（如果有的话）
  log.debug('开始为现有用户分配好友邀请码...')
  const existingUsers = await prisma.user.findMany()

  for (const user of existingUsers) {
    // 如果用户经验值大于等于50，分配3个好友邀请码
    if (user.score >= 50) {
      await allocateFriendCodes(user.id, 3)
      log.debug(`为用户 ${user.username} 分配了3个好友邀请码`)
    }
  }
  log.debug('好友邀请码分配完成！')

  log.debug('数据库种子数据创建完成！')
}

main()
  .catch((e) => {
    log.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
