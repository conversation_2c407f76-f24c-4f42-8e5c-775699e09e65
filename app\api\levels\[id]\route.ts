import { NextRequest, NextResponse } from 'next/server'
import { log } from '@/app/lib/logger';
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/app/lib/auth'
import { prisma } from '@/app/lib/db'
import { hasAdvancedAccess } from '@/app/lib/invite-codes'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const level = await prisma.level.findUnique({
      where: {
        id: resolvedParams.id
      },
      include: {
        tasks: {
          orderBy: {
            order: 'asc'
          }
        },
        progress: {
          where: {
            userId: session.user.id
          }
        },
        parent: true
      }
    })

    if (!level) {
      return NextResponse.json(
        { error: '关卡不存在' },
        { status: 404 }
      )
    }

    // 获取用户信息以检查权限
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { userType: true, score: true }
    })

    // 检查是否是受限关卡
    const parentName = level.parent?.name || level.name
    const hasAccess = hasAdvancedAccess(user?.userType || 'normal', user?.score || 0, parentName)

    if (!hasAccess) {
      const errorMessage = user?.userType === 'normal'
        ? (parentName === '进阶操作'
          ? '您需要达到500经验值才能访问此关卡'
          : '您需要达到600经验值才能访问此关卡')
        : (user?.userType === 'friend' && parentName === '实用技巧'
          ? '您需要达到600经验值才能访问此关卡'
          : '您没有权限访问此关卡，请使用邀请码注册以获得访问权限')

      return NextResponse.json(
        { error: errorMessage },
        { status: 403 }
      )
    }

    return NextResponse.json({
      ...level,
      hasAccess,
      isLocked: false
    })
  } catch (error) {
    log.error('获取关卡详情错误:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}