import { SvelteKitAuth } from '@auth/sveltekit'
import { PrismaAdapter } from '@auth/prisma-adapter'
import Credentials from '@auth/sveltekit/providers/credentials'
import bcrypt from 'bcryptjs'
import { prisma } from './db'

export const { handle, signIn, signOut } = SvelteKitAuth({
  adapter: PrismaAdapter(prisma),
  secret: process.env.AUTH_SECRET,
  providers: [
    Credentials({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email as string
          }
        })

        if (!user) {
          return null
        }

        // 检查邮箱是否已验证
        if (!user.emailVerified) {
          throw new Error('请先验证您的邮箱地址')
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password as string,
          user.password
        )

        if (!isPasswordValid) {
          return null
        }

        return {
          id: user.id,
          email: user.email,
          name: user.username,
          username: user.username,
          score: user.score,
          level: user.level,
          userType: user.userType
        }
      }
    })
  ],
  session: {
    strategy: 'jwt'
  },
  callbacks: {
    async jwt({ token, user, trigger }) {
      if (user) {
        token.username = user.username
        token.score = user.score
        token.level = user.level
        token.userType = user.userType
      }

      // 如果是更新触发，从数据库获取最新的用户信息
      if (trigger === 'update' && token.sub) {
        const updatedUser = await prisma.user.findUnique({
          where: { id: token.sub }
        })
        if (updatedUser) {
          token.username = updatedUser.username
          token.score = updatedUser.score
          token.level = updatedUser.level
          token.userType = updatedUser.userType
        }
      }

      return token
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.sub!
        session.user.username = token.username as string
        session.user.score = token.score as number
        session.user.level = token.level as number
        session.user.userType = token.userType as string
      }
      return session
    }
  },
  events: {
    async signOut() {
      // 确保登出时清除所有相关数据
      console.log('用户已登出，session已清除')
    }
  }
})
