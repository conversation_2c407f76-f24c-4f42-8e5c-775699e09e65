import { NextResponse } from 'next/server'

export async function GET(req: Request) {
  // 优先从代理头部获取原始域名和协议
  const host = 
    req.headers.get('x-forwarded-host') || // Vercel/AWS/Nginx 标准
    req.headers.get('host') || // 直接访问时的备用
    'localhost:3000'; // 回退值

  const protocol = 'https'; //使用EdgeOne之后都使用https
    // req.headers.get('x-forwarded-proto') || // 标准代理协议头
    // (req.url.startsWith('https') ? 'https' : 'http'); // 根据URL判断 //使用EdgeOne之后都使用https

  const baseUrl = `${protocol}://${host}`;

  // 根据环境变量决定是否允许爬虫
  const isProduction = process.env.NODE_ENV === 'production'

  const robotsContent = `User-agent: *
${isProduction ? 'Allow: /' : 'Disallow: /'}

# Baidu spider optimization
User-agent: Baiduspider
${isProduction ? 'Allow: /' : 'Disallow: /'}
Crawl-delay: 1

# Bing spider optimization
User-agent: bingbot
${isProduction ? 'Allow: /' : 'Disallow: /'}
Crawl-delay: 1

# Google spider optimization
User-agent: Googlebot
${isProduction ? 'Allow: /' : 'Disallow: /'}

# Disallowed paths for all crawlers
Disallow: /api/
Disallow: /auth/
Disallow: /_next/
Disallow: /admin/
Disallow: /profile/
Disallow: /dashboard/
Disallow: /task/
Disallow: /level/
Disallow: /invite-codes/

# Allowed public paths
Allow: /legal/
Allow: /

# Sitemap location
Sitemap: ${baseUrl}/sitemap.xml`

  return new NextResponse(robotsContent, {
    headers: {
      'Content-Type': 'text/plain; charset=utf-8',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
    },
  })
}
