'use client'

import { useSession } from 'next-auth/react'
import { log } from '@/app/lib/logger';
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface Task {
  id: string
  name: string
  description: string
  type: string
  order: number
  validation: string
  initialData?: string
}

interface Level {
  id: string
  name: string
  description: string
  difficulty: number
  points: number
  parentId?: string
  tasks: Task[]
  progress: Array<{
    completed: boolean
    score: number
    attempts: number
  }>
}

interface MainTask {
  id: string
  name: string
  description: string
  difficulty: number
  points: number
  children: Level[]
}

export default function TasksPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [mainTasks, setMainTasks] = useState<MainTask[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  useEffect(() => {
    if (session) {
      fetchLevels()
    }
  }, [session])

  const fetchLevels = async () => {
    try {
      const response = await fetch('/api/levels')
      if (response.ok) {
        const data = await response.json()
        setMainTasks(data)
      }
    } catch (error) {
      log.error('获取关卡失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateProgress = (level: Level) => {
    if (!level.progress || level.progress.length === 0) return 0
    return level.progress[0].completed ? 100 : 0
  }

  const getDifficultyText = (difficulty: number) => {
    switch (difficulty) {
      case 1:
        return '入门'
      case 2:
        return '初级'
      case 3:
        return '中级'
      case 4:
        return '困难'
      case 5:
        return '专家'
      default:
        return '未知'
    }
  }

  const getDifficultyColor = (difficulty: number) => {
    switch (difficulty) {
      case 1:
        return 'bg-green-100 text-green-800'
      case 2:
        return 'bg-blue-100 text-blue-800'
      case 3:
        return 'bg-yellow-100 text-yellow-800'
      case 4:
        return 'bg-orange-100 text-orange-800'
      case 5:
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-500"></div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  // 收集所有任务
  const allTasks: Array<{
    task: Task
    level: Level
    mainTask: MainTask
    progress: number
  }> = []

  mainTasks.forEach(mainTask => {
    mainTask.children.forEach(level => {
      level.tasks.forEach(task => {
        allTasks.push({
          task,
          level,
          mainTask,
          progress: calculateProgress(level)
        })
      })
    })
  })

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 sm:px-0">

          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              所有任务
            </h2>
            <p className="text-sm text-gray-600">
              查看所有可用的Excel学习任务
            </p>
          </div>

          {/* 任务列表 */}
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {allTasks.map(({ task, level, mainTask, progress }) => (
                <li key={task.id}>
                  <Link
                    href={`/task/${task.id}`}
                    className="block hover:bg-gray-50 px-4 py-4 sm:px-6"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-3">
                          <h3 className="text-sm font-medium text-gray-900 truncate">
                            {task.name}
                          </h3>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            getDifficultyColor(level.difficulty)
                          }`}>
                            {getDifficultyText(level.difficulty)}
                          </span>
                          {progress === 100 && (
                            <span className="text-green-500 text-sm">✓ 已闯过</span>
                          )}
                        </div>
                        <div className="mt-1">
                          <p className="text-sm text-gray-600">
                            {task.description.split('\n')[0]}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">
                            所属: {mainTask.name} → {level.name}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500">
                          {task.type}
                        </span>
                        <span className="text-sm font-medium text-blue-600">
                          {level.points} 经验值
                        </span>
                      </div>
                    </div>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {allTasks.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">暂无可用任务</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
