import type { NextConfig } from "next";

// Bundle分析器配置
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

const nextConfig: NextConfig = {
  reactStrictMode: false, // 禁用严格模式以避免Univer组件重复初始化

  // ▼▼▼ 新增缓存优化配置 ▼▼▼
    // generateBuildId: async () => {
    //   // 使用Git提交ID作为构建版本标识
    //   return process.env.GIT_COMMIT_SHA || 
    //         // 如果未设置环境变量，使用时间戳作为备选方案
    //         Date.now().toString();
    // },
    
    // assetPrefix: process.env.CDN_URL || undefined, // 使用CDN域名
    
    // 静态资源缓存控制
    // async headers() {
    //   return [
    //     {
    //       source: '/_next/static/:path*',
    //       headers: [
    //         {
    //           key: 'Cache-Control',
    //           value: 'public, max-age=31536000, immutable' // 1年长期缓存
    //         }
    //       ]
    //     },
    //     {
    //       source: '/login',
    //       headers: [
    //         {
    //           key: 'Cache-Control',
    //           value: 'no-cache, no-store, must-revalidate' // 登录页不缓存
    //         }
    //       ]
    //     }
    //   ]
    // },


  // 生产环境优化配置
  compiler: {
    // 在生产构建时移除console语句
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error'] // 保留console.error，移除其他console语句
    } : false,
  },

  // 环境变量配置
  env: {
    NEXT_PUBLIC_LOG_LEVEL: process.env.NODE_ENV === 'production' ? 'error' : 'debug',
  },

  // Webpack优化配置
  webpack: (config, { isServer, dev }) => {
    // 优化代码分割
    if (!isServer) {
      // 生产环境的额外优化
      if (!dev) {
        config.optimization.usedExports = true;
        config.optimization.sideEffects = false;
      }

      config.optimization.splitChunks = {
        ...config.optimization.splitChunks,
        chunks: 'all',
        minSize: 20000,
        maxSize: 244000,
        cacheGroups: {
          ...config.optimization.splitChunks.cacheGroups,
          // React相关包
          react: {
            test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
            name: 'react',
            chunks: 'all',
            priority: 40,
          },
          // Univer核心包 - 立即加载
          univerCore: {
            test: /[\\/]node_modules[\\/]@univerjs[\\/](core|design|ui|docs|sheets|engine-formula|engine-render)[\\/]/,
            name: 'univer-core',
            chunks: 'all',
            priority: 30,
            enforce: true,
          },
          // Univer基础功能包 - 懒加载
          univerBasic: {
            test: /[\\/]node_modules[\\/]@univerjs[\\/](sheets-filter|sheets-sort|sheets-conditional-formatting|sheets-table|sheets-data-validation|sheets-formula|sheets-numfmt)[\\/]/,
            name: 'univer-basic',
            chunks: 'async',
            priority: 20,
          },
          // Univer高级功能包 - 懒加载
          univerPremium: {
            test: /[\\/]node_modules[\\/]@univerjs-pro[\\/]/,
            name: 'univer-premium',
            chunks: 'async',
            priority: 10,
          },
          // 其他第三方库
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            priority: 5,
            minChunks: 2,
          },
        },
      };


    }

    return config;
  },
};

export default withBundleAnalyzer(nextConfig);
