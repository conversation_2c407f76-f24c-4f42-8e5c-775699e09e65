import { json } from '@sveltejs/kit'
import type { RequestHandler } from './$types'
import { prisma } from '$lib/db'

export const POST: RequestHandler = async ({ request, locals }) => {
  try {
    const session = await locals.auth()
    
    if (!session?.user) {
      return json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const { username } = await request.json()

    if (!username || !username.trim()) {
      return json(
        { error: '用户名不能为空' },
        { status: 400 }
      )
    }

    const trimmedUsername = username.trim()

    // 验证用户名长度
    if (trimmedUsername.length < 2 || trimmedUsername.length > 20) {
      return json(
        { error: '用户名长度必须在2-20个字符之间' },
        { status: 400 }
      )
    }

    // 检查用户名是否已被其他用户使用
    const existingUser = await prisma.user.findFirst({
      where: {
        username: trimmedUsername,
        id: {
          not: session.user.id
        }
      }
    })

    if (existingUser) {
      return json(
        { error: '该用户名已被使用，请选择其他用户名' },
        { status: 400 }
      )
    }

    // 更新用户名
    await prisma.user.update({
      where: { id: session.user.id },
      data: { username: trimmedUsername }
    })

    return json({ success: true })
  } catch (error) {
    console.error('更新用户名错误:', error)
    return json(
      { error: '服务器错误，请稍后重试' },
      { status: 500 }
    )
  }
}
