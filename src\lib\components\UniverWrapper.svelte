<script lang="ts">
  import { onMount } from 'svelte'
  import { browser } from '$app/environment'
  import type { UniverReadyCallback, UniverInstance, UniverAPI } from '$lib/types/univer'
  
  // Props
  export let onReady: UniverReadyCallback | undefined = undefined
  export let initialData: Record<string, unknown> | undefined = undefined
  
  // State
  let isLoading = true
  let loadingStage = '初始化中...'
  let UniverSheet: any = null
  
  const handleReady = (instance: UniverInstance, api: UniverAPI) => {
    // 延迟一点时间让用户看到加载完成的状态
    loadingStage = '加载完成！'
    setTimeout(() => {
      isLoading = false
      if (onReady) {
        onReady(instance, api)
      }
    }, 300)
  }
  
  // 模拟加载阶段更新
  onMount(async () => {
    if (!browser) return
    
    const stages = [
      { text: '正在加载核心组件...', delay: 100 },
      { text: '正在初始化表格...', delay: 800 },
      { text: '正在准备工作区...', delay: 1500 },
    ]
    
    const timers: NodeJS.Timeout[] = []
    
    stages.forEach(({ text, delay }) => {
      const timer = setTimeout(() => {
        if (isLoading) {
          loadingStage = text
        }
      }, delay)
      timers.push(timer)
    })
    
    // Dynamic import of UniverSheet component
    try {
      const module = await import('./UniverSheet.svelte')
      UniverSheet = module.default
    } catch (error) {
      console.error('Failed to load UniverSheet:', error)
    }
    
    // 清理定时器
    return () => {
      timers.forEach(timer => clearTimeout(timer))
    }
  })
</script>

<div class="relative h-full">
  {#if isLoading}
    <div class="absolute inset-0 z-10 flex flex-col items-center justify-start bg-white bg-opacity-95 rounded-lg backdrop-blur-sm">
      <div class="animate-spin rounded-full h-10 w-10 border-b-3 border-blue-600 mb-4 mt-16"></div>
      <div class="text-lg font-medium text-gray-800 mb-2">表格加载中，马上就好……</div>
      <div class="text-sm text-gray-600 animate-pulse">{loadingStage}</div>
    </div>
  {/if}
  
  {#if UniverSheet}
    <svelte:component this={UniverSheet} onReady={handleReady} {initialData} />
  {/if}
</div>

<style>
  .border-b-3 {
    border-bottom-width: 3px;
  }
</style>
